
import React from 'react';

import BookingCalendar from '@/components/BookingCalendar';
import FAQSection from '@/components/FAQSection';
import FormPreconnect from '@/components/FormPreconnect';
import InternalLinks from '@/components/InternalLinks';
import LocalBusinessSchema from '@/components/SEO/LocalBusinessSchema';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';
import SEOBreadcrumbs from '@/components/SEOBreadcrumbs';

import { generateMetadata as generateSEOMetadata  } from '../metadata';

// Booking FAQs data
const bookingFAQs = [
  {
    question: "Jak mogę dokonać rezerwacji?",
    answer: "Rezerwacji możesz dokonać przez nasz formularz online, WhatsApp lub bezpośrednio przez telefon. Wybierz dogodny dla Ciebie termin i wypełnij formularz kontaktowy."
  },
  {
    question: "<PERSON><PERSON><PERSON> są opcje płatności?",
    answer: "Akceptuje<PERSON> płatności przelewem bankowym, kartą płatniczą oraz gotówką. Zaliczka wynosi 30% wartości usługi, reszta płatna przed rozpoczęciem."
  },
  {
    question: "Czy mogę anulować rezerwację?",
    answer: "Tak, rezerwację można anulować do 48 godzin przed terminem bez dodatkowych opłat. W przypadku późniejszej anulacji pobieramy 50% zaliczki."
  },
  {
    question: "Co jeśli pogoda będzie niesprzyjająca?",
    answer: "W przypadku złej pogody oferujemy przełożenie terminu lub pełny zwrot kosztów. Bezpieczeństwo naszych gości jest najważniejsze."
  },
  {
    question: "Czy potrzebuję doświadczenia?",
    answer: "Nie, nasze programy są dostosowane do wszystkich poziomów zaawansowania. Zapewniamy pełne instruktaże i wsparcie doświadczonych przewodników."
  }
];

export const metadata = generateSEOMetadata({
  title: 'Rezerwacja Retreatu - Kalendarz Dostępności',
  description: 'Sprawdź dostępne terminy retreatów jogowych na Bali i Sri Lance. Zarezerwuj swoje miejsce na niezapomnianej przygodzie.',
  keywords: ['rezerwacja retreat', 'kalendarz', 'dostępność', 'booking', 'retreat jogowy Bali'],
});

export default function RezerwacjaPage() {
  return (
    <>
      <LocalBusinessSchema 
        businessType="TravelAgency"
        location="Warszawa"
      />
      
      <SEOBreadcrumbs 
        customBreadcrumbs={[
          { href: '/rezerwacja', label: 'Rezerwacja Retreatu' }
        ]}
      />
      
      <FormPreconnect />
      <main className="py-section min-h-screen">
      <div className="max-w-7xl mx-auto container-padding">
        <header className="text-center mb-2xl">
          <h1 className="text-4xl md:text-5xl font-cormorant text-charcoal mb-md font-light /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
            Rezerwacja <span className="gradient-text">Retreatu</span>
          </h1>
          <p className="text-lg text-charcoal-light max-w-3xl mx-auto leading-relaxed mb-lg font-light">
            Wybierz termin, który Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej 
            przygodzie jogowej na Bali lub Sri Lance.
          </p>
          <div className="decorative-line" />
        </header>

        <BookingCalendar />

        {/* Additional Info Section */}
        <section className="mt-20 grid md:grid-cols-2 gap-xl">
          <div className="bg-charcoal/5 p-8">
            <h3 className="text-xl font-cormorant text-charcoal mb-sm /* TODO: Replace with CardTitle */">
              Jak przebiega rezerwacja?
            </h3>
            <div className="space-y-sm text-charcoal-light">
              <div className="flex items-start gap-3">
                <span className="bg-charcoal text-white w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">1</span>
                <div>
                  <strong>Wybierz termin</strong> - Kliknij na wybrany retreat w kalendarzu
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-charcoal text-white w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">2</span>
                <div>
                  <strong>Wypełnij formularz</strong> - Podaj dane osobowe i preferencje
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-charcoal text-white w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">3</span>
                <div>
                  <strong>Wpłać zadatek</strong> - 30% wartości retreatu (dane w emailu)
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-charcoal text-white w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5">4</span>
                <div>
                  <strong>Otrzymaj potwierdzenie</strong> - Email z wszystkimi szczegółami
                </div>
              </div>
            </div>
          </div>

          <div className="bg-terra/5 p-8">
            <h3 className="text-xl font-cormorant text-charcoal mb-sm">
              Co jest wliczone w cenę?
            </h3>
            <div className="space-y-3 text-charcoal-light">
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>7 nocy w hotelu (pokój dzielony)</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Wszystkie posiłki (śniadanie, lunch, kolacja)</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Transport lotnisko-hotel-lotnisko</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Praktyka jogi 2x dziennie</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Medytacje i warsztaty</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Zwiedzanie i wycieczki</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-charcoal">✓</span>
                <span>Opieka instruktora przez cały pobyt</span>
              </div>
            </div>
            
            <div className="mt-md pt-6 border-t border-charcoal/10">
              <h4 className="font-medium text-charcoal mb-2">Dodatkowo płatne:</h4>
              <div className="space-y-2 text-sm text-charcoal-light">
                <div>• Pokój jednoosobowy: +500 PLN</div>
                <div>• Bilety lotnicze (pomoc w organizacji)</div>
                <div>• Ubezpieczenie podróżne</div>
                <div>• Wydatki osobiste i pamiątki</div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section with Structured Data */}
        <FAQSection
          faqs={bookingFAQs}
          title="Często zadawane pytania o rezerwację"
          subtitle="Znajdź odpowiedzi na pytania dotyczące procesu rezerwacji i płatności"
          className="mt-20"
        />

        {/* Contact CTA */}
        <section className="mt-20 text-center">
          <div className="bg-gradient-to-r from-charcoal/5 to-golden/5 p-12">
            <h3 className="text-2xl font-cormorant text-charcoal mb-sm /* TODO: Replace with SectionTitle */" /* TODO: Replace with CardTitle */>
              Masz pytania?
            </h3>
            <p className="text-charcoal-light mb-lg max-w-2xl mx-auto">
              Jeśli nie znalazłeś odpowiedzi na swoje pytanie lub potrzebujesz pomocy 
              z rezerwacją, skontaktuj się z nami bezpośrednio.
            </p>
            <div className="flex flex-col sm:flex-row gap-sm justify-center">
              <div className="flex justify-center">
                <PerformantWhatsApp 
                  size="md"
                  variant="button"
                  className="px-hero-padding py-3"
                  message="Cześć Julia! Mam pytania dotyczące rezerwacji retreatu. Czy możesz mi pomóc?"
                />
              </div>
              <a
                href="mailto:<EMAIL>"
                className="btn-unified-secondary"
              >
                Napisz email
              </a>
              <a
                href="tel:+48606101523"
                className="btn-unified-secondary"
              >
                Zadzwoń: +48 606 101 523
              </a>
            </div>
          </div>
        </section>

        {/* Internal Links for SEO */}
        <InternalLinks currentPage="rezerwacja" />
      </div>

      {/* Floating WhatsApp */}
      <div className="fixed bottom-6 right-6 z-50">
        <PerformantWhatsApp
          size="lg"
          message="Cześć Julia! Chciałabym/chciałbym zarezerwować miejsce na retreatie. Jakie są dostępne terminy?"
        />
      </div>
    </main>
    </>
  );
}
