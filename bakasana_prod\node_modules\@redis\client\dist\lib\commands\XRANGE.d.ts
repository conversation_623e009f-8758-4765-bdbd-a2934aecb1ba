import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
interface XRangeOptions {
    COUNT?: number;
}
export declare function transformArguments(key: RedisCommandArgument, start: RedisCommandArgument, end: RedisCommandArgument, options?: XRangeOptions): RedisCommandArguments;
export { transformStreamMessagesReply as transformReply } from './generic-transformers';
