import { RedisCommandArgument, RedisCommandArguments } from '.';
import { LMPopOptions, ListSide } from './generic-transformers';
export declare const FIRST_KEY_INDEX = 2;
export declare function transformArguments(keys: RedisCommandArgument | Array<RedisCommandArgument>, side: ListSide, options?: LMPopOptions): RedisCommandArguments;
export declare function transformReply(): null | [
    key: string,
    elements: Array<string>
];
