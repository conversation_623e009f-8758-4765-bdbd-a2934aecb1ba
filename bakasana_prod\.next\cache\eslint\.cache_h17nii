[{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx": "1", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\page.jsx": "2", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js": "3", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js": "4", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js": "5", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js": "6", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js": "7", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\fitssey\\webhook\\route.js": "8", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js": "9", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\performance\\report\\route.js": "10", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\send\\route.js": "11", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\subscribe\\route.js": "12", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\test\\route.js": "13", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\unsubscribe\\route.js": "14", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\BlogPageClientContent.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx": "16", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx": "17", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\metadata.js": "18", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\page.jsx": "19", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js": "20", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\page.jsx": "21", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js": "23", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\page.jsx": "24", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\hero-demo\\page.jsx": "25", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx": "26", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\julia-jakubowicz-instruktor\\page.jsx": "27", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\ContactForm.jsx": "28", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js": "29", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx": "30", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx": "31", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\mapa\\page.jsx": "32", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js": "33", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found-bali.jsx": "34", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx": "35", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\layout.jsx": "36", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\metadata.js": "37", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx": "38", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\opengraph-image.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\polityka-prywatnosci\\page.jsx": "42", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js": "43", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\page.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\srilanka\\page.jsx": "45", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js": "46", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\page.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js": "48", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\page.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty-jogi-bali-2025\\page.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\rezerwacja\\page.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js": "52", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js": "53", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\wellness\\page.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\yoga-retreat-z-polski\\page.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js": "57", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx": "58", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\metadata.js": "59", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\page.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx": "61", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityEnhancer.jsx": "62", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityProvider.jsx": "63", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ABTestingEngine.jsx": "64", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AdvancedAnalytics.jsx": "65", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AnalyticsProvider.jsx": "66", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AnalyticsWrapper.jsx": "67", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ConversionOptimization.jsx": "68", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnhancedAnalytics.jsx": "69", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnterpriseAnalytics.jsx": "70", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ErrorTracking.jsx": "71", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\GoogleAnalytics.jsx": "72", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\index.jsx": "73", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\LazyAnalytics.jsx": "74", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SEOTracker.jsx": "75", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SuperiorAnalytics.jsx": "76", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\TravelAnalytics.jsx": "77", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\AnimatedCounter.jsx": "78", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BlogWhatsAppCTA.jsx": "79", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Booking\\EnhancedBookingFlow.jsx": "80", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingCalendar.jsx": "81", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingForm.jsx": "82", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Breadcrumbs.jsx": "83", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientInteractiveButton.jsx": "84", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientLayout.jsx": "85", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnly.jsx": "86", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnlyResponsiveChecker.jsx": "87", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ConditionalNavbar.jsx": "88", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CookieConsent.jsx": "89", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CoreWebVitals.jsx": "90", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CriticalCSS.jsx": "91", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ErrorBoundary\\AdvancedErrorBoundary.jsx": "92", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ErrorBoundary.jsx": "93", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Events\\EventCard.jsx": "94", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FAQSection.jsx": "95", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseyIntegration.jsx": "96", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseySchedule.jsx": "97", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\index.jsx": "98", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx": "99", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\ServerFooter.jsx": "100", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FormPreconnect.jsx": "101", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\GhostNavbar.jsx": "102", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx": "103", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\HighlightCard\\index.jsx": "104", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\BakasanaHero.jsx": "105", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\CustomColorHero.jsx": "106", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ElegantBakasanaHero.jsx": "107", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\HeroVariantsDemo.jsx": "108", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\MinimalistYogaHero.jsx": "109", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\OnlineClassesSection.jsx": "110", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHero.jsx": "111", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHomePage.jsx": "112", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\SimpleHero.jsx": "113", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\WellnessPage.jsx": "114", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InteractiveMap.jsx": "115", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinks.jsx": "116", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinksComponent.jsx": "117", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InvestmentSection.jsx": "118", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryElements.jsx": "119", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryScrollProgress.jsx": "120", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryTestimonials.jsx": "121", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryWhatsApp.jsx": "122", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistHero.jsx": "123", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistNavbar.jsx": "124", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistRetreatCard.jsx": "125", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\ClientNavbar.jsx": "126", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\index.jsx": "127", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx": "128", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\NewsletterSignup.jsx": "129", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OnlineClassesStyles.jsx": "130", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedBreadcrumbs.jsx": "131", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedIcon.jsx": "132", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedImage.jsx": "133", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerfectNavbar.jsx": "134", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\ImageOptimizer.jsx": "135", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\LoadingStates.jsx": "136", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\PerformanceMonitor.jsx": "137", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\PerformanceMonitoring.jsx": "138", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\RealUserMonitoring.jsx": "139", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerformanceHints.jsx": "140", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerformantWhatsApp.jsx": "141", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\PWAInstall.jsx": "142", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\PWAManager.jsx": "143", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js": "144", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWAInstaller.jsx": "145", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\QuickCTA.jsx": "146", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveChecker.jsx": "147", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveCheckerWrapper.jsx": "148", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\RetreatCalendar.jsx": "149", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityRetreats.jsx": "150", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityTestimonials.jsx": "151", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ScrollReveal.jsx": "152", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\AdvancedSEO.jsx": "153", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CanonicalURL.jsx": "154", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CompetitorAnalysis.jsx": "155", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\ContactStructuredData.jsx": "156", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnhancedStructuredData.jsx": "157", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnterpriseMetaTags.jsx": "158", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\LocalBusinessSchema.jsx": "159", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\MetaTags.jsx": "160", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\SchemaOrg.jsx": "161", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\TravelSEO.jsx": "162", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEOBreadcrumbs.jsx": "163", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx": "164", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SmartBreadcrumbs.jsx": "165", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TestimonialSlider.jsx": "166", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Toast.jsx": "167", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TransformationCTA.jsx": "168", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\CurrencyConverter.jsx": "169", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\DestinationCard.jsx": "170", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\InteractiveMap.jsx": "171", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\TravelGuide.jsx": "172", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\WeatherWidget.jsx": "173", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TrustBadges.jsx": "174", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx": "175", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\BlogComponents.jsx": "176", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx": "177", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\card.jsx": "178", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ElegantQuote.jsx": "179", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\EnhancedButton.jsx": "180", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ErrorBoundary.jsx": "181", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\GlassCard.jsx": "182", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\IconSystem.jsx": "183", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js": "184", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\input.jsx": "185", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\label.jsx": "186", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\LazyImage.jsx": "187", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedImage.jsx": "188", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedLazyImage.jsx": "189", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageLoader.jsx": "190", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageTransition.jsx": "191", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ParallaxSection.jsx": "192", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssurance.jsx": "193", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssuranceWrapper.jsx": "194", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ResponsiveGrid.jsx": "195", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\RippleButton.jsx": "196", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\Section.jsx": "197", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SkeletonLoader.jsx": "198", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SmoothScrollProvider.jsx": "199", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\textarea.jsx": "200", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedButton.jsx": "201", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedCard.jsx": "202", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedInput.jsx": "203", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedTypography.jsx": "204", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WebVitals.jsx": "205", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\AccessibilityEnhancer.jsx": "206", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\AmbientBackground.jsx": "207", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\ContextualCursor.jsx": "208", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js": "209", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\KeyboardShortcuts.jsx": "210", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\MagneticButton.jsx": "211", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\MicroAnimations.jsx": "212", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\NoiseTexture.jsx": "213", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\OpticalTypography.jsx": "214", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\PerformanceMonitor.jsx": "215", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\ProgressIndicator.jsx": "216", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\SmartPreloader.jsx": "217", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\SmoothScrollIndicator.jsx": "218", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\StaggeredReveal.jsx": "219", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\WellnessProvider.jsx": "220", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\WorldClassProvider.jsx": "221", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js": "222", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js": "223", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js": "224", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\investmentData.js": "225", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js": "226", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js": "227", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js": "228", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\testimonialsData.js": "229", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js": "230", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useFormValidation.js": "231", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js": "232", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js": "233", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\errorHandler.js": "234", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js": "235", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js": "236", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js": "237", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js": "238", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware-redirects.js": "239", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js": "240", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\services\\pushNotifications.js": "241"}, {"size": 12226, "mtime": 1753389874755, "results": "242", "hashOfConfig": "243"}, {"size": 12045, "mtime": 1753389892612, "results": "244", "hashOfConfig": "243"}, {"size": 4769, "mtime": 1753385124692, "results": "245", "hashOfConfig": "243"}, {"size": 5837, "mtime": 1753385124692, "results": "246", "hashOfConfig": "243"}, {"size": 6943, "mtime": 1753453921583, "results": "247", "hashOfConfig": "243"}, {"size": 2316, "mtime": 1753385124692, "results": "248", "hashOfConfig": "243"}, {"size": 7495, "mtime": 1753385124692, "results": "249", "hashOfConfig": "243"}, {"size": 6520, "mtime": 1753385124692, "results": "250", "hashOfConfig": "243"}, {"size": 2391, "mtime": 1753385124692, "results": "251", "hashOfConfig": "243"}, {"size": 9742, "mtime": 1753389744605, "results": "252", "hashOfConfig": "243"}, {"size": 6018, "mtime": 1753389154437, "results": "253", "hashOfConfig": "243"}, {"size": 5227, "mtime": 1753389110730, "results": "254", "hashOfConfig": "243"}, {"size": 2613, "mtime": 1753389170725, "results": "255", "hashOfConfig": "243"}, {"size": 1845, "mtime": 1753389124629, "results": "256", "hashOfConfig": "243"}, {"size": 10667, "mtime": 1753385245967, "results": "257", "hashOfConfig": "243"}, {"size": 14373, "mtime": 1753385246000, "results": "258", "hashOfConfig": "243"}, {"size": 10833, "mtime": 1753385246020, "results": "259", "hashOfConfig": "243"}, {"size": 1797, "mtime": 1753385124692, "results": "260", "hashOfConfig": "243"}, {"size": 1585, "mtime": 1753385246033, "results": "261", "hashOfConfig": "243"}, {"size": 1573, "mtime": 1753385124692, "results": "262", "hashOfConfig": "243"}, {"size": 9682, "mtime": 1753385245947, "results": "263", "hashOfConfig": "243"}, {"size": 2023, "mtime": 1753385246042, "results": "264", "hashOfConfig": "243"}, {"size": 1851, "mtime": 1753385124692, "results": "265", "hashOfConfig": "243"}, {"size": 6890, "mtime": 1753385246062, "results": "266", "hashOfConfig": "243"}, {"size": 311, "mtime": 1753385246071, "results": "267", "hashOfConfig": "243"}, {"size": 15939, "mtime": 1753388203390, "results": "268", "hashOfConfig": "243"}, {"size": 22222, "mtime": 1753388203391, "results": "269", "hashOfConfig": "243"}, {"size": 12205, "mtime": 1753385246154, "results": "270", "hashOfConfig": "243"}, {"size": 1840, "mtime": 1753385124692, "results": "271", "hashOfConfig": "243"}, {"size": 5594, "mtime": 1753388203360, "results": "272", "hashOfConfig": "243"}, {"size": 6660, "mtime": 1753452804985, "results": "273", "hashOfConfig": "243"}, {"size": 10814, "mtime": 1753385246196, "results": "274", "hashOfConfig": "243"}, {"size": 4644, "mtime": 1753385124692, "results": "275", "hashOfConfig": "243"}, {"size": 5076, "mtime": 1753385246215, "results": "276", "hashOfConfig": "243"}, {"size": 319, "mtime": 1753385246220, "results": "277", "hashOfConfig": "243"}, {"size": 761, "mtime": 1753385246227, "results": "278", "hashOfConfig": "243"}, {"size": 1806, "mtime": 1753385124692, "results": "279", "hashOfConfig": "243"}, {"size": 9207, "mtime": 1753390072260, "results": "280", "hashOfConfig": "243"}, {"size": 951, "mtime": 1753385124692, "results": "281", "hashOfConfig": "282"}, {"size": 4678, "mtime": 1753385246250, "results": "283", "hashOfConfig": "243"}, {"size": 18976, "mtime": 1753385246267, "results": "284", "hashOfConfig": "243"}, {"size": 9916, "mtime": 1753385246284, "results": "285", "hashOfConfig": "243"}, {"size": 3724, "mtime": 1753385124692, "results": "286", "hashOfConfig": "243"}, {"size": 40237, "mtime": 1753389347427, "results": "287", "hashOfConfig": "243"}, {"size": 13428, "mtime": 1753388203393, "results": "288", "hashOfConfig": "243"}, {"size": 2650, "mtime": 1753385124692, "results": "289", "hashOfConfig": "243"}, {"size": 907, "mtime": 1753388203363, "results": "290", "hashOfConfig": "243"}, {"size": 1969, "mtime": 1753385124692, "results": "291", "hashOfConfig": "243"}, {"size": 16364, "mtime": 1753390969477, "results": "292", "hashOfConfig": "243"}, {"size": 16577, "mtime": 1753388203394, "results": "293", "hashOfConfig": "243"}, {"size": 9318, "mtime": 1753454304129, "results": "294", "hashOfConfig": "243"}, {"size": 1892, "mtime": 1753385124692, "results": "295", "hashOfConfig": "243"}, {"size": 6778, "mtime": 1753385124692, "results": "296", "hashOfConfig": "243"}, {"size": 22760, "mtime": 1753388203394, "results": "297", "hashOfConfig": "243"}, {"size": 427, "mtime": 1753385246448, "results": "298", "hashOfConfig": "243"}, {"size": 24739, "mtime": 1753388203395, "results": "299", "hashOfConfig": "243"}, {"size": 1383, "mtime": 1753385124692, "results": "300", "hashOfConfig": "243"}, {"size": 18442, "mtime": 1753390114092, "results": "301", "hashOfConfig": "243"}, {"size": 1698, "mtime": 1753385124692, "results": "302", "hashOfConfig": "243"}, {"size": 24294, "mtime": 1753390149724, "results": "303", "hashOfConfig": "243"}, {"size": 5652, "mtime": 1753385124692, "results": "304", "hashOfConfig": "282"}, {"size": 13672, "mtime": 1753385246530, "results": "305", "hashOfConfig": "243"}, {"size": 12761, "mtime": 1753308299823, "results": "306", "hashOfConfig": "243"}, {"size": 16029, "mtime": 1753385246566, "results": "307", "hashOfConfig": "243"}, {"size": 7747, "mtime": 1753308299822, "results": "308", "hashOfConfig": "243"}, {"size": 8064, "mtime": 1753388646128, "results": "309", "hashOfConfig": "243"}, {"size": 758, "mtime": 1753385246578, "results": "310", "hashOfConfig": "243"}, {"size": 15629, "mtime": 1753385246604, "results": "311", "hashOfConfig": "243"}, {"size": 15426, "mtime": 1753388401053, "results": "312", "hashOfConfig": "243"}, {"size": 28170, "mtime": 1753308299821, "results": "313", "hashOfConfig": "243"}, {"size": 8319, "mtime": 1753388531814, "results": "314", "hashOfConfig": "243"}, {"size": 1293, "mtime": 1753385246627, "results": "315", "hashOfConfig": "243"}, {"size": 384, "mtime": 1753385246633, "results": "316", "hashOfConfig": "243"}, {"size": 4304, "mtime": 1753385246640, "results": "317", "hashOfConfig": "243"}, {"size": 17592, "mtime": 1753385246664, "results": "318", "hashOfConfig": "243"}, {"size": 16577, "mtime": 1753308299819, "results": "319", "hashOfConfig": "243"}, {"size": 18261, "mtime": 1753385246700, "results": "320", "hashOfConfig": "243"}, {"size": 2497, "mtime": 1753385246707, "results": "321", "hashOfConfig": "243"}, {"size": 3178, "mtime": 1753385246714, "results": "322", "hashOfConfig": "243"}, {"size": 41218, "mtime": 1753306423354, "results": "323", "hashOfConfig": "243"}, {"size": 11305, "mtime": 1753390310065, "results": "324", "hashOfConfig": "243"}, {"size": 19715, "mtime": 1753389928561, "results": "325", "hashOfConfig": "243"}, {"size": 4577, "mtime": 1753385246757, "results": "326", "hashOfConfig": "243"}, {"size": 1423, "mtime": 1753385246765, "results": "327", "hashOfConfig": "243"}, {"size": 848, "mtime": 1753385246773, "results": "328", "hashOfConfig": "243"}, {"size": 555, "mtime": 1753385246781, "results": "329", "hashOfConfig": "243"}, {"size": 6444, "mtime": 1753385246804, "results": "330", "hashOfConfig": "243"}, {"size": 280, "mtime": 1753385246809, "results": "331", "hashOfConfig": "243"}, {"size": 3088, "mtime": 1753385246817, "results": "332", "hashOfConfig": "243"}, {"size": 5243, "mtime": 1753385246827, "results": "333", "hashOfConfig": "243"}, {"size": 12875, "mtime": 1753385246832, "results": "334", "hashOfConfig": "243"}, {"size": 5088, "mtime": 1753385246852, "results": "335", "hashOfConfig": "243"}, {"size": 527, "mtime": 1753385246838, "results": "336", "hashOfConfig": "243"}, {"size": 2341, "mtime": 1753385246862, "results": "337", "hashOfConfig": "243"}, {"size": 1793, "mtime": 1753390743566, "results": "338", "hashOfConfig": "243"}, {"size": 5967, "mtime": 1753391345118, "results": "339", "hashOfConfig": "243"}, {"size": 9875, "mtime": 1753308299731, "results": "340", "hashOfConfig": "243"}, {"size": 13972, "mtime": 1753385246892, "results": "341", "hashOfConfig": "243"}, {"size": 11698, "mtime": 1753385124692, "results": "342", "hashOfConfig": "282"}, {"size": 2979, "mtime": 1753385246900, "results": "343", "hashOfConfig": "243"}, {"size": 435, "mtime": 1753385246904, "results": "344", "hashOfConfig": "243"}, {"size": 8508, "mtime": 1753384143598, "results": "345", "hashOfConfig": "243"}, {"size": 8876, "mtime": 1753385124692, "results": "346", "hashOfConfig": "282"}, {"size": 826, "mtime": 1753385246917, "results": "347", "hashOfConfig": "243"}, {"size": 9754, "mtime": 1753388203399, "results": "348", "hashOfConfig": "243"}, {"size": 11425, "mtime": 1753388203399, "results": "349", "hashOfConfig": "243"}, {"size": 11898, "mtime": 1753388203399, "results": "350", "hashOfConfig": "243"}, {"size": 2867, "mtime": 1753385246963, "results": "351", "hashOfConfig": "243"}, {"size": 8544, "mtime": 1753388203400, "results": "352", "hashOfConfig": "243"}, {"size": 2549, "mtime": 1753385246989, "results": "353", "hashOfConfig": "243"}, {"size": 7514, "mtime": 1753388203400, "results": "354", "hashOfConfig": "243"}, {"size": 16684, "mtime": 1753388203400, "results": "355", "hashOfConfig": "243"}, {"size": 9487, "mtime": 1753388203401, "results": "356", "hashOfConfig": "243"}, {"size": 18498, "mtime": 1753385247050, "results": "357", "hashOfConfig": "243"}, {"size": 13233, "mtime": 1753387623579, "results": "358", "hashOfConfig": "243"}, {"size": 1843, "mtime": 1753390903274, "results": "359", "hashOfConfig": "243"}, {"size": 1843, "mtime": 1753390903274, "results": "360", "hashOfConfig": "243"}, {"size": 15870, "mtime": 1753385247098, "results": "361", "hashOfConfig": "243"}, {"size": 10619, "mtime": 1753385247120, "results": "362", "hashOfConfig": "243"}, {"size": 7520, "mtime": 1753385247140, "results": "363", "hashOfConfig": "243"}, {"size": 10614, "mtime": 1753385247160, "results": "364", "hashOfConfig": "243"}, {"size": 7447, "mtime": 1753385247176, "results": "365", "hashOfConfig": "243"}, {"size": 11169, "mtime": 1753385247228, "results": "366", "hashOfConfig": "243"}, {"size": 4704, "mtime": 1753308299725, "results": "367", "hashOfConfig": "243"}, {"size": 3923, "mtime": 1753296794422, "results": "368", "hashOfConfig": "243"}, {"size": 6916, "mtime": 1753308299793, "results": "369", "hashOfConfig": "243"}, {"size": 42, "mtime": 1752409529209, "results": "370", "hashOfConfig": "243"}, {"size": 5989, "mtime": 1753385124692, "results": "371", "hashOfConfig": "282"}, {"size": 9321, "mtime": 1753308299723, "results": "372", "hashOfConfig": "243"}, {"size": 4073, "mtime": 1753385247270, "results": "373", "hashOfConfig": "243"}, {"size": 2651, "mtime": 1753385247288, "results": "374", "hashOfConfig": "243"}, {"size": 5781, "mtime": 1753385247300, "results": "375", "hashOfConfig": "243"}, {"size": 8756, "mtime": 1753391106177, "results": "376", "hashOfConfig": "243"}, {"size": 11257, "mtime": 1753385247332, "results": "377", "hashOfConfig": "243"}, {"size": 9909, "mtime": 1753385247350, "results": "378", "hashOfConfig": "243"}, {"size": 9650, "mtime": 1753385247372, "results": "379", "hashOfConfig": "243"}, {"size": 9371, "mtime": 1753385247390, "results": "380", "hashOfConfig": "243"}, {"size": 10929, "mtime": 1753389744605, "results": "381", "hashOfConfig": "243"}, {"size": 11739, "mtime": 1753385247409, "results": "382", "hashOfConfig": "243"}, {"size": 1781, "mtime": 1753385247415, "results": "383", "hashOfConfig": "243"}, {"size": 6203, "mtime": 1753385247424, "results": "384", "hashOfConfig": "243"}, {"size": 14538, "mtime": 1753388203403, "results": "385", "hashOfConfig": "243"}, {"size": 11470, "mtime": 1753389041980, "results": "386", "hashOfConfig": "243"}, {"size": 12794, "mtime": 1753385124692, "results": "387", "hashOfConfig": "243"}, {"size": 9929, "mtime": 1753388992018, "results": "388", "hashOfConfig": "243"}, {"size": 727, "mtime": 1753385247464, "results": "389", "hashOfConfig": "243"}, {"size": 6280, "mtime": 1753385247475, "results": "390", "hashOfConfig": "243"}, {"size": 669, "mtime": 1753385247481, "results": "391", "hashOfConfig": "243"}, {"size": 2164, "mtime": 1753385247488, "results": "392", "hashOfConfig": "243"}, {"size": 9611, "mtime": 1753311191195, "results": "393", "hashOfConfig": "243"}, {"size": 5962, "mtime": 1753385247508, "results": "394", "hashOfConfig": "243"}, {"size": 5812, "mtime": 1753385247519, "results": "395", "hashOfConfig": "243"}, {"size": 7916, "mtime": 1753385247537, "results": "396", "hashOfConfig": "243"}, {"size": 658, "mtime": 1753385247542, "results": "397", "hashOfConfig": "243"}, {"size": 14826, "mtime": 1753385247560, "results": "398", "hashOfConfig": "243"}, {"size": 2259, "mtime": 1753385247567, "results": "399", "hashOfConfig": "243"}, {"size": 11507, "mtime": 1753385247580, "results": "400", "hashOfConfig": "243"}, {"size": 19139, "mtime": 1753385247596, "results": "401", "hashOfConfig": "243"}, {"size": 6850, "mtime": 1753385247604, "results": "402", "hashOfConfig": "243"}, {"size": 2545, "mtime": 1753385247612, "results": "403", "hashOfConfig": "243"}, {"size": 2496, "mtime": 1753385247619, "results": "404", "hashOfConfig": "243"}, {"size": 13696, "mtime": 1753385247636, "results": "405", "hashOfConfig": "243"}, {"size": 5936, "mtime": 1753385247647, "results": "406", "hashOfConfig": "243"}, {"size": 8229, "mtime": 1753385124692, "results": "407", "hashOfConfig": "282"}, {"size": 5950, "mtime": 1753308299674, "results": "408", "hashOfConfig": "243"}, {"size": 6226, "mtime": 1753385247663, "results": "409", "hashOfConfig": "243"}, {"size": 2011, "mtime": 1753306423176, "results": "410", "hashOfConfig": "243"}, {"size": 4015, "mtime": 1753385247672, "results": "411", "hashOfConfig": "243"}, {"size": 14162, "mtime": 1753385247693, "results": "412", "hashOfConfig": "243"}, {"size": 5758, "mtime": 1753385247703, "results": "413", "hashOfConfig": "243"}, {"size": 9183, "mtime": 1753385247718, "results": "414", "hashOfConfig": "243"}, {"size": 16888, "mtime": 1753306423306, "results": "415", "hashOfConfig": "243"}, {"size": 12645, "mtime": 1753306423304, "results": "416", "hashOfConfig": "243"}, {"size": 3107, "mtime": 1753385247736, "results": "417", "hashOfConfig": "243"}, {"size": 999, "mtime": 1753385247744, "results": "418", "hashOfConfig": "243"}, {"size": 8607, "mtime": 1753385247756, "results": "419", "hashOfConfig": "243"}, {"size": 2122, "mtime": 1753385247762, "results": "420", "hashOfConfig": "243"}, {"size": 1574, "mtime": 1753385247768, "results": "421", "hashOfConfig": "243"}, {"size": 2269, "mtime": 1753385247774, "results": "422", "hashOfConfig": "243"}, {"size": 6529, "mtime": 1753385247783, "results": "423", "hashOfConfig": "243"}, {"size": 6315, "mtime": 1753454027985, "results": "424", "hashOfConfig": "243"}, {"size": 5786, "mtime": 1753385247804, "results": "425", "hashOfConfig": "243"}, {"size": 7431, "mtime": 1753385247820, "results": "426", "hashOfConfig": "243"}, {"size": 2189, "mtime": 1753385124693, "results": "427", "hashOfConfig": "243"}, {"size": 726, "mtime": 1753385247830, "results": "428", "hashOfConfig": "243"}, {"size": 385, "mtime": 1753385247835, "results": "429", "hashOfConfig": "243"}, {"size": 3685, "mtime": 1753385247844, "results": "430", "hashOfConfig": "243"}, {"size": 2048, "mtime": 1753385247850, "results": "431", "hashOfConfig": "243"}, {"size": 5344, "mtime": 1753385247862, "results": "432", "hashOfConfig": "243"}, {"size": 900, "mtime": 1753385247866, "results": "433", "hashOfConfig": "243"}, {"size": 8412, "mtime": 1753385247880, "results": "434", "hashOfConfig": "243"}, {"size": 9993, "mtime": 1753308299752, "results": "435", "hashOfConfig": "243"}, {"size": 8693, "mtime": 1753385247900, "results": "436", "hashOfConfig": "243"}, {"size": 839, "mtime": 1753385247907, "results": "437", "hashOfConfig": "243"}, {"size": 6816, "mtime": 1753385247916, "results": "438", "hashOfConfig": "243"}, {"size": 3270, "mtime": 1753385247923, "results": "439", "hashOfConfig": "243"}, {"size": 3476, "mtime": 1753385247929, "results": "440", "hashOfConfig": "243"}, {"size": 9500, "mtime": 1753385247941, "results": "441", "hashOfConfig": "243"}, {"size": 4351, "mtime": 1753385247957, "results": "442", "hashOfConfig": "243"}, {"size": 612, "mtime": 1753385247962, "results": "443", "hashOfConfig": "243"}, {"size": 5373, "mtime": 1753385247970, "results": "444", "hashOfConfig": "243"}, {"size": 4679, "mtime": 1753385247979, "results": "445", "hashOfConfig": "243"}, {"size": 6681, "mtime": 1753385247996, "results": "446", "hashOfConfig": "243"}, {"size": 8047, "mtime": 1753385248008, "results": "447", "hashOfConfig": "243"}, {"size": 1303, "mtime": 1753385248015, "results": "448", "hashOfConfig": "243"}, {"size": 10695, "mtime": 1753306423259, "results": "449", "hashOfConfig": "243"}, {"size": 2635, "mtime": 1753385248029, "results": "450", "hashOfConfig": "243"}, {"size": 7207, "mtime": 1753176752178, "results": "451", "hashOfConfig": "243"}, {"size": 1173, "mtime": 1753385124693, "results": "452", "hashOfConfig": "243"}, {"size": 11044, "mtime": 1753306423258, "results": "453", "hashOfConfig": "243"}, {"size": 2725, "mtime": 1753385248052, "results": "454", "hashOfConfig": "243"}, {"size": 10072, "mtime": 1753176752179, "results": "455", "hashOfConfig": "243"}, {"size": 1196, "mtime": 1753385248069, "results": "456", "hashOfConfig": "243"}, {"size": 5574, "mtime": 1753385248080, "results": "457", "hashOfConfig": "243"}, {"size": 9723, "mtime": 1753306423255, "results": "458", "hashOfConfig": "243"}, {"size": 5818, "mtime": 1753176752180, "results": "459", "hashOfConfig": "243"}, {"size": 6059, "mtime": 1753385248108, "results": "460", "hashOfConfig": "243"}, {"size": 5698, "mtime": 1753176752181, "results": "461", "hashOfConfig": "243"}, {"size": 2556, "mtime": 1753385248124, "results": "462", "hashOfConfig": "243"}, {"size": 8286, "mtime": 1753176752181, "results": "463", "hashOfConfig": "243"}, {"size": 10971, "mtime": 1753308299744, "results": "464", "hashOfConfig": "243"}, {"size": 17158, "mtime": 1753385124693, "results": "465", "hashOfConfig": "243"}, {"size": 1131, "mtime": 1753385124693, "results": "466", "hashOfConfig": "243"}, {"size": 1876, "mtime": 1753385124693, "results": "467", "hashOfConfig": "243"}, {"size": 9039, "mtime": 1753385124693, "results": "468", "hashOfConfig": "243"}, {"size": 2150, "mtime": 1753385124693, "results": "469", "hashOfConfig": "243"}, {"size": 11343, "mtime": 1753385124693, "results": "470", "hashOfConfig": "243"}, {"size": 19254, "mtime": 1753385124693, "results": "471", "hashOfConfig": "243"}, {"size": 6727, "mtime": 1753385124693, "results": "472", "hashOfConfig": "243"}, {"size": 10467, "mtime": 1753385124693, "results": "473", "hashOfConfig": "243"}, {"size": 6640, "mtime": 1753385124693, "results": "474", "hashOfConfig": "243"}, {"size": 1951, "mtime": 1753385124693, "results": "475", "hashOfConfig": "243"}, {"size": 15562, "mtime": 1753385124693, "results": "476", "hashOfConfig": "243"}, {"size": 9925, "mtime": 1753453486575, "results": "477", "hashOfConfig": "243"}, {"size": 19444, "mtime": 1753385124693, "results": "478", "hashOfConfig": "243"}, {"size": 10060, "mtime": 1753385124693, "results": "479", "hashOfConfig": "243"}, {"size": 2141, "mtime": 1753385124693, "results": "480", "hashOfConfig": "243"}, {"size": 4748, "mtime": 1753385124693, "results": "481", "hashOfConfig": "243"}, {"size": 1352, "mtime": 1753385124693, "results": "482", "hashOfConfig": "243"}, {"size": 2826, "mtime": 1753385124693, "results": "483", "hashOfConfig": "243"}, {"size": 9915, "mtime": 1753389082494, "results": "484", "hashOfConfig": "243"}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "122nttn", {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x2gad9", {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx", ["1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\page.jsx", ["1216", "1217", "1218"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", ["1219", "1220", "1221", "1222"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", ["1223", "1224", "1225", "1226"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", ["1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", ["1239", "1240"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", ["1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\fitssey\\webhook\\route.js", ["1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", ["1264", "1265"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\performance\\report\\route.js", ["1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\send\\route.js", ["1278", "1279", "1280", "1281"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\subscribe\\route.js", ["1282", "1283", "1284", "1285", "1286", "1287", "1288"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\test\\route.js", ["1289", "1290"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\push\\unsubscribe\\route.js", ["1291", "1292", "1293"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\BlogPageClientContent.jsx", ["1294", "1295", "1296"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", ["1297", "1298", "1299", "1300", "1301", "1302"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", ["1303", "1304", "1305"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\page.jsx", ["1306", "1307", "1308", "1309"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx", ["1310", "1311", "1312", "1313", "1314"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\page.jsx", ["1315", "1316", "1317", "1318"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\hero-demo\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx", ["1319", "1320", "1321", "1322", "1323"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\juli<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", ["1324", "1325", "1326", "1327", "1328", "1329", "1330"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\ContactForm.jsx", ["1331", "1332", "1333", "1334"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx", ["1335", "1336"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx", ["1337", "1338"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\mapa\\page.jsx", ["1339"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found-bali.jsx", ["1340", "1341", "1342", "1343", "1344", "1345"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\layout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx", ["1346", "1347", "1348", "1349"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\opengraph-image.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page.jsx", ["1350", "1351", "1352"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\polityka-prywatnosci\\page.jsx", ["1353", "1354", "1355"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\page.jsx", ["1356"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\srilanka\\page.jsx", ["1357", "1358", "1359", "1360", "1361", "1362", "1363"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\page.jsx", ["1364", "1365", "1366", "1367", "1368", "1369", "1370"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty-jogi-bali-2025\\page.jsx", ["1371", "1372", "1373", "1374", "1375", "1376", "1377"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\rezerwacja\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx", ["1378", "1379", "1380", "1381", "1382"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\wellness\\page.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\yoga-retreat-z-polski\\page.jsx", ["1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx", ["1391", "1392", "1393"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\page.jsx", ["1394", "1395", "1396", "1397", "1398"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityEnhancer.jsx", ["1399"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityProvider.jsx", ["1400"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ABTestingEngine.jsx", ["1401", "1402", "1403", "1404"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AdvancedAnalytics.jsx", ["1405"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AnalyticsProvider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AnalyticsWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ConversionOptimization.jsx", ["1406", "1407", "1408", "1409", "1410"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnhancedAnalytics.jsx", ["1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnterpriseAnalytics.jsx", ["1421"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ErrorTracking.jsx", ["1422", "1423"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\GoogleAnalytics.jsx", ["1424", "1425"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\LazyAnalytics.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SEOTracker.jsx", ["1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SuperiorAnalytics.jsx", ["1434"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\TravelAnalytics.jsx", ["1435", "1436", "1437", "1438"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\AnimatedCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BlogWhatsAppCTA.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Booking\\EnhancedBookingFlow.jsx", ["1439"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingCalendar.jsx", ["1440", "1441", "1442", "1443", "1444", "1445", "1446"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingForm.jsx", ["1447"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Breadcrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientInteractiveButton.jsx", ["1448"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnly.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnlyResponsiveChecker.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ConditionalNavbar.jsx", ["1449"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CookieConsent.jsx", ["1450"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CoreWebVitals.jsx", ["1451", "1452", "1453", "1454", "1455"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CriticalCSS.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ErrorBoundary\\AdvancedErrorBoundary.jsx", ["1456", "1457"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ErrorBoundary.jsx", ["1458", "1459", "1460"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Events\\EventCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FAQSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseyIntegration.jsx", ["1461", "1462"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseySchedule.jsx", ["1463"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", ["1464"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\ServerFooter.jsx", ["1465"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FormPreconnect.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\GhostNavbar.jsx", ["1466"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", ["1467"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\HighlightCard\\index.jsx", ["1468", "1469", "1470"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\BakasanaHero.jsx", ["1471"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\CustomColorHero.jsx", ["1472"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ElegantBakasanaHero.jsx", ["1473"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\HeroVariantsDemo.jsx", ["1474"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\MinimalistYogaHero.jsx", ["1475"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\OnlineClassesSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHero.jsx", ["1476"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHomePage.jsx", ["1477"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\SimpleHero.jsx", ["1478"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\WellnessPage.jsx", ["1479", "1480", "1481", "1482", "1483", "1484"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InteractiveMap.jsx", ["1485", "1486", "1487", "1488"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinks.jsx", ["1489", "1490"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinksComponent.jsx", ["1491", "1492"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InvestmentSection.jsx", ["1493", "1494", "1495", "1496", "1497", "1498"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryElements.jsx", ["1499", "1500", "1501", "1502", "1503"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryScrollProgress.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryTestimonials.jsx", ["1504", "1505", "1506", "1507", "1508"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\LuxuryWhatsApp.jsx", ["1509"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistHero.jsx", ["1510"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistNavbar.jsx", ["1511"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistRetreatCard.jsx", ["1512"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\ClientNavbar.jsx", ["1513"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\NewsletterSignup.jsx", ["1514"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OnlineClassesStyles.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedBreadcrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedImage.jsx", ["1515", "1516"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerfectNavbar.jsx", ["1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\ImageOptimizer.jsx", ["1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\LoadingStates.jsx", ["1535"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\PerformanceMonitor.jsx", ["1536", "1537", "1538"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\PerformanceMonitoring.jsx", ["1539", "1540"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\RealUserMonitoring.jsx", ["1541", "1542", "1543"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerformanceHints.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerformantWhatsApp.jsx", ["1544", "1545"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\PWAInstall.jsx", ["1546"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\PWAManager.jsx", ["1547", "1548", "1549", "1550", "1551", "1552", "1553"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js", ["1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWAInstaller.jsx", ["1577"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\QuickCTA.jsx", ["1578"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveChecker.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveCheckerWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\RetreatCalendar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityRetreats.jsx", ["1579"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityTestimonials.jsx", ["1580", "1581", "1582"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ScrollReveal.jsx", ["1583"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\AdvancedSEO.jsx", ["1584"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CanonicalURL.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CompetitorAnalysis.jsx", ["1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\ContactStructuredData.jsx", ["1595"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnhancedStructuredData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnterpriseMetaTags.jsx", ["1596"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\LocalBusinessSchema.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\MetaTags.jsx", ["1597"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\SchemaOrg.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\TravelSEO.jsx", ["1598"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEOBreadcrumbs.jsx", ["1599"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SmartBreadcrumbs.jsx", ["1600"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TestimonialSlider.jsx", ["1601", "1602", "1603", "1604"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Toast.jsx", ["1605"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TransformationCTA.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\CurrencyConverter.jsx", ["1606", "1607", "1608", "1609", "1610"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\DestinationCard.jsx", ["1611", "1612", "1613", "1614", "1615"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\InteractiveMap.jsx", ["1616"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\TravelGuide.jsx", ["1617"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\WeatherWidget.jsx", ["1618"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TrustBadges.jsx", ["1619", "1620", "1621"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\BlogComponents.jsx", ["1622", "1623", "1624"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\card.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ElegantQuote.jsx", ["1625", "1626", "1627"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\EnhancedButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ErrorBoundary.jsx", ["1628", "1629", "1630"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\GlassCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\IconSystem.jsx", ["1631"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\label.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\LazyImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedLazyImage.jsx", ["1632", "1633", "1634"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageLoader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageTransition.jsx", ["1635", "1636", "1637", "1638"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ParallaxSection.jsx", ["1639"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssurance.jsx", ["1640"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssuranceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ResponsiveGrid.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\RippleButton.jsx", ["1641"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\Section.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SkeletonLoader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SmoothScrollProvider.jsx", ["1642"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\textarea.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedButton.jsx", ["1643"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedTypography.jsx", ["1644", "1645"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WebVitals.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\AccessibilityEnhancer.jsx", ["1646"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\AmbientBackground.jsx", ["1647"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\ContextualCursor.jsx", ["1648"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\KeyboardShortcuts.jsx", ["1649"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\MagneticButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\MicroAnimations.jsx", ["1650"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\NoiseTexture.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\OpticalTypography.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\PerformanceMonitor.jsx", ["1651"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\ProgressIndicator.jsx", ["1652"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\SmartPreloader.jsx", ["1653", "1654"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\SmoothScrollIndicator.jsx", ["1655"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\StaggeredReveal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\WellnessProvider.jsx", ["1656"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\WorldClassProvider.jsx", ["1657"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", ["1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", ["1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\investmentData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\testimonialsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", ["1678"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useFormValidation.js", ["1679"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\errorHandler.js", ["1680", "1681", "1682", "1683", "1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js", ["1686", "1687"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware-redirects.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\services\\pushNotifications.js", ["1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706"], [], {"ruleId": "1707", "severity": 1, "message": "1708", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "1711"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 15, "suggestions": "1713"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "1715"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 11, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 21, "suggestions": "1717"}, {"ruleId": "1718", "severity": 1, "message": "1719", "line": 29, "column": 6, "nodeType": "1720", "endLine": 29, "endColumn": 14, "suggestions": "1721"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 49, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 49, "endColumn": 20, "suggestions": "1726"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 63, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 63, "endColumn": 20, "suggestions": "1727"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 83, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 83, "endColumn": 20, "suggestions": "1728"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 12, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 21, "suggestions": "1729"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 47, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 47, "endColumn": 20, "suggestions": "1730"}, {"ruleId": "1707", "severity": 1, "message": "1731", "line": 74, "column": 14, "nodeType": "1709", "messageId": "1710", "endLine": 74, "endColumn": 19}, {"ruleId": "1707", "severity": 1, "message": "1731", "line": 69, "column": 12, "nodeType": "1709", "messageId": "1710", "endLine": 69, "endColumn": 17}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 102, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 102, "endColumn": 18, "suggestions": "1732"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 157, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 157, "endColumn": 16, "suggestions": "1733"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 165, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 165, "endColumn": 18, "suggestions": "1734"}, {"ruleId": "1707", "severity": 1, "message": "1731", "line": 67, "column": 12, "nodeType": "1709", "messageId": "1710", "endLine": 67, "endColumn": 17}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 97, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 97, "endColumn": 18, "suggestions": "1735"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 163, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 163, "endColumn": 18, "suggestions": "1736"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 208, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 208, "endColumn": 18, "suggestions": "1737"}, {"ruleId": "1707", "severity": 1, "message": "1738", "line": 1, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 1, "endColumn": 14, "suggestions": "1739"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 23, "column": 15, "nodeType": "1724", "messageId": "1725", "endLine": 23, "endColumn": 27, "suggestions": "1740"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 32, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 32, "endColumn": 21, "suggestions": "1741"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 37, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 37, "endColumn": 20, "suggestions": "1742"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 43, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 43, "endColumn": 19, "suggestions": "1743"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 93, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 93, "endColumn": 21, "suggestions": "1744"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 125, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 125, "endColumn": 21, "suggestions": "1745"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 144, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 144, "endColumn": 21, "suggestions": "1746"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 186, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 186, "endColumn": 19, "suggestions": "1747"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 223, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 223, "endColumn": 18, "suggestions": "1748"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 242, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 242, "endColumn": 18, "suggestions": "1749"}, {"ruleId": "1707", "severity": 1, "message": "1750", "line": 248, "column": 16, "nodeType": "1709", "messageId": "1710", "endLine": 248, "endColumn": 28, "suggestions": "1751"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 58, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 58, "endColumn": 19, "suggestions": "1752"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 66, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 66, "endColumn": 18, "suggestions": "1753"}, {"ruleId": "1707", "severity": 1, "message": "1754", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 17, "suggestions": "1755"}, {"ruleId": "1707", "severity": 1, "message": "1756", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 18, "suggestions": "1757"}, {"ruleId": "1707", "severity": 1, "message": "1758", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 17, "suggestions": "1759"}, {"ruleId": "1707", "severity": 1, "message": "1760", "line": 50, "column": 7, "nodeType": "1709", "messageId": "1710", "endLine": 50, "endColumn": 20}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 71, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 71, "endColumn": 17, "suggestions": "1761"}, {"ruleId": "1707", "severity": 1, "message": "1762", "line": 171, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 171, "endColumn": 18, "suggestions": "1763"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 189, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 189, "endColumn": 19, "suggestions": "1764"}, {"ruleId": "1707", "severity": 1, "message": "1765", "line": 213, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 213, "endColumn": 21, "suggestions": "1766"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 12, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 12, "endColumn": 15, "suggestions": "1767"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 28, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 28, "endColumn": 20, "suggestions": "1768"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 35, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 35, "endColumn": 16, "suggestions": "1769"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 55, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 55, "endColumn": 20, "suggestions": "1770"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 63, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 63, "endColumn": 18, "suggestions": "1771"}, {"ruleId": "1707", "severity": 1, "message": "1772", "line": 86, "column": 5, "nodeType": "1709", "messageId": "1710", "endLine": 86, "endColumn": 16, "suggestions": "1773"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 89, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 89, "endColumn": 14, "suggestions": "1774"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 128, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 128, "endColumn": 18, "suggestions": "1775"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 136, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 136, "endColumn": 14, "suggestions": "1776"}, {"ruleId": "1707", "severity": 1, "message": "1777", "line": 151, "column": 53, "nodeType": "1709", "messageId": "1710", "endLine": 151, "endColumn": 67, "suggestions": "1778"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 153, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 153, "endColumn": 14, "suggestions": "1779"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 170, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 170, "endColumn": 14, "suggestions": "1780"}, {"ruleId": "1707", "severity": 1, "message": "1765", "line": 193, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 193, "endColumn": 21, "suggestions": "1781"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 220, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 220, "endColumn": 14, "suggestions": "1782"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 246, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 246, "endColumn": 18, "suggestions": "1783"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 21, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 21, "endColumn": 19, "suggestions": "1784"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 64, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 64, "endColumn": 18, "suggestions": "1785"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 66, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 66, "endColumn": 16, "suggestions": "1786"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 69, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 69, "endColumn": 18, "suggestions": "1787"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 116, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 116, "endColumn": 20, "suggestions": "1788"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 123, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 123, "endColumn": 18, "suggestions": "1789"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 179, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 179, "endColumn": 20, "suggestions": "1790"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 186, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 186, "endColumn": 18, "suggestions": "1791"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 211, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 211, "endColumn": 14, "suggestions": "1792"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 270, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 270, "endColumn": 20, "suggestions": "1793"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 273, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 273, "endColumn": 20, "suggestions": "1794"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 276, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 276, "endColumn": 20, "suggestions": "1795"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 282, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 282, "endColumn": 18, "suggestions": "1796"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 336, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 336, "endColumn": 18, "suggestions": "1797"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 34, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 34, "endColumn": 16, "suggestions": "1798"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 45, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 45, "endColumn": 18, "suggestions": "1799"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 108, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 108, "endColumn": 20, "suggestions": "1800"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 115, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 115, "endColumn": 20, "suggestions": "1801"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 33, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 33, "endColumn": 16, "suggestions": "1802"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 34, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 34, "endColumn": 16, "suggestions": "1803"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 46, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 46, "endColumn": 20, "suggestions": "1804"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 56, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 56, "endColumn": 18, "suggestions": "1805"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 111, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 111, "endColumn": 16, "suggestions": "1806"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 113, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 113, "endColumn": 18, "suggestions": "1807"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 150, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 150, "endColumn": 18, "suggestions": "1808"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 63, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 63, "endColumn": 18, "suggestions": "1809"}, {"ruleId": "1707", "severity": 1, "message": "1810", "line": 76, "column": 27, "nodeType": "1709", "messageId": "1710", "endLine": 76, "endColumn": 34, "suggestions": "1811"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 26, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 26, "endColumn": 18, "suggestions": "1812"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 27, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 27, "endColumn": 18, "suggestions": "1813"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 41, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 41, "endColumn": 18, "suggestions": "1814"}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 4, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 13, "suggestions": "1816"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 289, "column": 15, "nodeType": "1819", "messageId": "1820", "suggestions": "1821"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 289, "column": 46, "nodeType": "1819", "messageId": "1820", "suggestions": "1822"}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 1, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 1, "endColumn": 13, "suggestions": "1823"}, {"ruleId": "1707", "severity": 1, "message": "1824", "line": 2, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 2, "endColumn": 12, "suggestions": "1825"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "1826"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "1827"}, {"ruleId": "1707", "severity": 1, "message": "1828", "line": 11, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 14, "suggestions": "1829"}, {"ruleId": "1707", "severity": 1, "message": "1830", "line": 19, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 19, "endColumn": 14, "suggestions": "1831"}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 1, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 1, "endColumn": 13, "suggestions": "1832"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "1833"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "1834"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 12, "suggestions": "1835"}, {"ruleId": "1707", "severity": 1, "message": "1836", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 12, "suggestions": "1837"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 10, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 11, "suggestions": "1838"}, {"ruleId": "1707", "severity": 1, "message": "1731", "line": 244, "column": 12, "nodeType": "1709", "messageId": "1710", "endLine": 244, "endColumn": 17}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 5, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 21, "suggestions": "1839"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 12, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 12, "endColumn": 21, "suggestions": "1840"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 14, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 14, "endColumn": 23, "suggestions": "1841"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 17, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 17, "endColumn": 23, "suggestions": "1842"}, {"ruleId": "1707", "severity": 1, "message": "1843", "line": 20, "column": 14, "nodeType": "1709", "messageId": "1710", "endLine": 20, "endColumn": 26}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 5, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 12, "suggestions": "1844"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 15, "suggestions": "1845"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 8, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 11, "suggestions": "1846"}, {"ruleId": "1847", "severity": 2, "message": "1848", "line": 163, "column": 15, "nodeType": "1849", "endLine": 167, "endColumn": 16}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 106, "column": 13, "nodeType": "1852", "messageId": "1853", "endLine": 106, "endColumn": 52}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 304, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 304, "endColumn": 48}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 312, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 312, "endColumn": 48}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 322, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 322, "endColumn": 42}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 330, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 330, "endColumn": 55}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 187, "column": 19, "nodeType": "1852", "messageId": "1853", "endLine": 187, "endColumn": 75}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 367, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 367, "endColumn": 65}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 375, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 375, "endColumn": 61}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 385, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 385, "endColumn": 51}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 393, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 393, "endColumn": 56}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 434, "column": 57, "nodeType": "1819", "messageId": "1820", "suggestions": "1854"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 434, "column": 76, "nodeType": "1819", "messageId": "1820", "suggestions": "1855"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 4, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 21, "suggestions": "1856"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 122, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 122, "endColumn": 20, "suggestions": "1857"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 352, "column": 13, "nodeType": "1819", "messageId": "1820", "suggestions": "1858"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 352, "column": 58, "nodeType": "1819", "messageId": "1820", "suggestions": "1859"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 72, "column": 15, "nodeType": "1819", "messageId": "1820", "suggestions": "1860"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 72, "column": 62, "nodeType": "1819", "messageId": "1820", "suggestions": "1861"}, {"ruleId": "1707", "severity": 1, "message": "1862", "line": 25, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 25, "endColumn": 34, "suggestions": "1863"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 172, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 172, "endColumn": 20, "suggestions": "1864"}, {"ruleId": "1847", "severity": 2, "message": "1848", "line": 254, "column": 15, "nodeType": "1849", "endLine": 254, "endColumn": 68}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 66, "column": 15, "nodeType": "1819", "messageId": "1820", "suggestions": "1865"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 66, "column": 29, "nodeType": "1819", "messageId": "1820", "suggestions": "1866"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 69, "column": 26, "nodeType": "1819", "messageId": "1820", "suggestions": "1867"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 69, "column": 42, "nodeType": "1819", "messageId": "1820", "suggestions": "1868"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 101, "column": 15, "nodeType": "1819", "messageId": "1820", "suggestions": "1869"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 101, "column": 72, "nodeType": "1819", "messageId": "1820", "suggestions": "1870"}, {"ruleId": "1707", "severity": 1, "message": "1824", "line": 5, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 12, "suggestions": "1871"}, {"ruleId": "1707", "severity": 1, "message": "1872", "line": 15, "column": 11, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 20, "suggestions": "1873"}, {"ruleId": "1707", "severity": 1, "message": "1874", "line": 15, "column": 22, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 30, "suggestions": "1875"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 99, "column": 15, "nodeType": "1852", "messageId": "1853", "endLine": 99, "endColumn": 57}, {"ruleId": "1707", "severity": 1, "message": "1876", "line": 12, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 19, "suggestions": "1877"}, {"ruleId": "1707", "severity": 1, "message": "1878", "line": 14, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 14, "endColumn": 18, "suggestions": "1879"}, {"ruleId": "1707", "severity": 1, "message": "1880", "line": 16, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 16, "endColumn": 25, "suggestions": "1881"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 11, "suggestions": "1882"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 44, "column": 37, "nodeType": "1819", "messageId": "1820", "suggestions": "1883"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 44, "column": 55, "nodeType": "1819", "messageId": "1820", "suggestions": "1884"}, {"ruleId": "1885", "severity": 1, "message": "1886", "line": 38, "column": 1, "nodeType": "1887", "endLine": 929, "endColumn": 2}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 12, "suggestions": "1888"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 8, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 15, "suggestions": "1889"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "1890"}, {"ruleId": "1707", "severity": 1, "message": "1828", "line": 13, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 13, "endColumn": 14, "suggestions": "1891"}, {"ruleId": "1707", "severity": 1, "message": "1892", "line": 32, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 32, "endColumn": 20, "suggestions": "1893"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 104, "column": 13, "nodeType": "1852", "messageId": "1853", "endLine": 104, "endColumn": 55}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 173, "column": 15, "nodeType": "1852", "messageId": "1853", "endLine": 173, "endColumn": 38}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "1894"}, {"ruleId": "1707", "severity": 1, "message": "1836", "line": 8, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 12, "suggestions": "1895"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "1896"}, {"ruleId": "1707", "severity": 1, "message": "1897", "line": 14, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 14, "endColumn": 39, "suggestions": "1898"}, {"ruleId": "1707", "severity": 1, "message": "1899", "line": 15, "column": 30, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 42, "suggestions": "1900"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 350, "column": 13, "nodeType": "1819", "messageId": "1820", "suggestions": "1901"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 351, "column": 30, "nodeType": "1819", "messageId": "1820", "suggestions": "1902"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 86, "column": 13, "nodeType": "1852", "messageId": "1853", "endLine": 86, "endColumn": 62}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 251, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 251, "endColumn": 54}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 259, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 259, "endColumn": 53}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 269, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 269, "endColumn": 49}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 277, "column": 21, "nodeType": "1852", "messageId": "1853", "endLine": 277, "endColumn": 46}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 318, "column": 57, "nodeType": "1819", "messageId": "1820", "suggestions": "1903"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 318, "column": 76, "nodeType": "1819", "messageId": "1820", "suggestions": "1904"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 10, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 19, "suggestions": "1905"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 10, "column": 46, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 54, "suggestions": "1906"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 146, "column": 13, "nodeType": "1852", "messageId": "1853", "endLine": 146, "endColumn": 67}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 348, "column": 51, "nodeType": "1819", "messageId": "1820", "suggestions": "1907"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 348, "column": 71, "nodeType": "1819", "messageId": "1820", "suggestions": "1908"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 10, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 19, "suggestions": "1909"}, {"ruleId": "1707", "severity": 1, "message": "1836", "line": 10, "column": 35, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 44, "suggestions": "1910"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 10, "column": 46, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 54, "suggestions": "1911"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 139, "column": 13, "nodeType": "1852", "messageId": "1853", "endLine": 139, "endColumn": 65}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 462, "column": 23, "nodeType": "1852", "messageId": "1853", "endLine": 462, "endColumn": 66}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 470, "column": 23, "nodeType": "1852", "messageId": "1853", "endLine": 470, "endColumn": 72}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 480, "column": 23, "nodeType": "1852", "messageId": "1853", "endLine": 480, "endColumn": 63}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 488, "column": 23, "nodeType": "1852", "messageId": "1853", "endLine": 488, "endColumn": 57}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 4, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 21, "suggestions": "1912"}, {"ruleId": "1707", "severity": 1, "message": "1913", "line": 14, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 14, "endColumn": 19, "suggestions": "1914"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 285, "column": 17, "nodeType": "1852", "messageId": "1853", "endLine": 285, "endColumn": 53}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 3, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 13, "suggestions": "1915"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 9, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 21, "suggestions": "1916"}, {"ruleId": "1707", "severity": 1, "message": "1917", "line": 9, "column": 25, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 34, "suggestions": "1918"}, {"ruleId": "1707", "severity": 1, "message": "1919", "line": 11, "column": 53, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 66, "suggestions": "1920"}, {"ruleId": "1707", "severity": 1, "message": "1921", "line": 22, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 22, "endColumn": 23, "suggestions": "1922"}, {"ruleId": "1718", "severity": 1, "message": "1923", "line": 137, "column": 6, "nodeType": "1720", "endLine": 137, "endColumn": 18, "suggestions": "1924"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1925", "line": 38, "column": 0}, {"ruleId": "1718", "severity": 1, "message": "1926", "line": 306, "column": 6, "nodeType": "1720", "endLine": 306, "endColumn": 8, "suggestions": "1927"}, {"ruleId": "1707", "severity": 1, "message": "1928", "line": 429, "column": 52, "nodeType": "1709", "messageId": "1710", "endLine": 429, "endColumn": 62, "suggestions": "1929"}, {"ruleId": "1718", "severity": 1, "message": "1930", "line": 434, "column": 6, "nodeType": "1720", "endLine": 434, "endColumn": 46, "suggestions": "1931"}, {"ruleId": "1718", "severity": 1, "message": "1932", "line": 538, "column": 6, "nodeType": "1720", "endLine": 538, "endColumn": 32, "suggestions": "1933"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1934", "line": 227, "column": 0}, {"ruleId": "1707", "severity": 1, "message": "1935", "line": 383, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 383, "endColumn": 24, "suggestions": "1936"}, {"ruleId": "1707", "severity": 1, "message": "1937", "line": 459, "column": 11, "nodeType": "1709", "messageId": "1710", "endLine": 459, "endColumn": 14, "suggestions": "1938"}, {"ruleId": "1707", "severity": 1, "message": "1939", "line": 554, "column": 39, "nodeType": "1709", "messageId": "1710", "endLine": 554, "endColumn": 40, "suggestions": "1940"}, {"ruleId": "1707", "severity": 1, "message": "1939", "line": 566, "column": 40, "nodeType": "1709", "messageId": "1710", "endLine": 566, "endColumn": 41, "suggestions": "1941"}, {"ruleId": "1718", "severity": 1, "message": "1930", "line": 594, "column": 6, "nodeType": "1720", "endLine": 594, "endColumn": 42, "suggestions": "1942"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 98, "column": 15, "nodeType": "1724", "messageId": "1725", "endLine": 98, "endColumn": 26, "suggestions": "1943"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 101, "column": 15, "nodeType": "1724", "messageId": "1725", "endLine": 101, "endColumn": 28, "suggestions": "1944"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 193, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 193, "endColumn": 24, "suggestions": "1945"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 196, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 196, "endColumn": 26, "suggestions": "1946"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 284, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 284, "endColumn": 24, "suggestions": "1947"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 287, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 287, "endColumn": 26, "suggestions": "1948"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 332, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 332, "endColumn": 24, "suggestions": "1949"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 335, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 335, "endColumn": 26, "suggestions": "1950"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 366, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 366, "endColumn": 24, "suggestions": "1951"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 369, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 369, "endColumn": 26, "suggestions": "1952"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1953", "line": 790, "column": 0}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 79, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 79, "endColumn": 20, "suggestions": "1954"}, {"ruleId": "1707", "severity": 1, "message": "1955", "line": 241, "column": 15, "nodeType": "1709", "messageId": "1710", "endLine": 241, "endColumn": 22, "suggestions": "1956"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 10, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 10, "endColumn": 17, "suggestions": "1957"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 23, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 23, "endColumn": 24, "suggestions": "1958"}, {"ruleId": "1707", "severity": 1, "message": "1959", "line": 218, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 218, "endColumn": 30, "suggestions": "1960"}, {"ruleId": "1718", "severity": 1, "message": "1961", "line": 319, "column": 6, "nodeType": "1720", "endLine": 319, "endColumn": 8, "suggestions": "1962"}, {"ruleId": "1707", "severity": 1, "message": "1963", "line": 369, "column": 29, "nodeType": "1709", "messageId": "1710", "endLine": 369, "endColumn": 39, "suggestions": "1964"}, {"ruleId": "1718", "severity": 1, "message": "1965", "line": 406, "column": 6, "nodeType": "1720", "endLine": 406, "endColumn": 8, "suggestions": "1966"}, {"ruleId": "1718", "severity": 1, "message": "1961", "line": 527, "column": 6, "nodeType": "1720", "endLine": 527, "endColumn": 19, "suggestions": "1967"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 565, "column": 23, "nodeType": "1724", "messageId": "1725", "endLine": 565, "endColumn": 36}, {"ruleId": "1718", "severity": 1, "message": "1968", "line": 610, "column": 6, "nodeType": "1720", "endLine": 610, "endColumn": 35, "suggestions": "1969"}, {"ruleId": "1718", "severity": 1, "message": "1968", "line": 624, "column": 6, "nodeType": "1720", "endLine": 624, "endColumn": 67, "suggestions": "1970"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1971", "line": 438, "column": 0}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 34, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 34, "endColumn": 16, "suggestions": "1972"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 542, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 542, "endColumn": 20, "suggestions": "1973"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 588, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 588, "endColumn": 20, "suggestions": "1974"}, {"ruleId": "1707", "severity": 1, "message": "1975", "line": 650, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 650, "endColumn": 15, "suggestions": "1976"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1977", "line": 14, "column": 32}, {"ruleId": "1707", "severity": 1, "message": "1978", "line": 3, "column": 20, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 27, "suggestions": "1979"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 7, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 19, "suggestions": "1980"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 7, "column": 21, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 33, "suggestions": "1981"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 7, "column": 46, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 54, "suggestions": "1982"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 8, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 21, "suggestions": "1983"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 30, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 30, "endColumn": 17, "suggestions": "1984"}, {"ruleId": "1707", "severity": 1, "message": "1985", "line": 84, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 84, "endColumn": 25, "suggestions": "1986"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1987", "line": 528, "column": 70}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 5, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 21, "suggestions": "1988"}, {"ruleId": "1707", "severity": 1, "message": "1989", "line": 7, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 17, "suggestions": "1990"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 5, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 21, "suggestions": "1991"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 161, "column": 20, "nodeType": "1724", "messageId": "1725", "endLine": 161, "endColumn": 31}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 162, "column": 20, "nodeType": "1724", "messageId": "1725", "endLine": 162, "endColumn": 31}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 163, "column": 20, "nodeType": "1724", "messageId": "1725", "endLine": 163, "endColumn": 31}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 164, "column": 20, "nodeType": "1724", "messageId": "1725", "endLine": 164, "endColumn": 31}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 165, "column": 21, "nodeType": "1724", "messageId": "1725", "endLine": 165, "endColumn": 32}, {"ruleId": "1707", "severity": 1, "message": "1992", "line": 12, "column": 35, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 40, "suggestions": "1993"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 23, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 23, "endColumn": 18, "suggestions": "1994"}, {"ruleId": "1707", "severity": 1, "message": "1992", "line": 11, "column": 35, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 40, "suggestions": "1995"}, {"ruleId": "1707", "severity": 1, "message": "1992", "line": 15, "column": 21, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 26, "suggestions": "1996"}, {"ruleId": "1707", "severity": 1, "message": "1997", "line": 15, "column": 28, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 37, "suggestions": "1998"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 7, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 21, "suggestions": "1999"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 45, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 45, "endColumn": 20, "suggestions": "2000"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2001", "line": 107, "column": 1}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 6, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 21, "suggestions": "2002"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2003", "line": 6, "column": 9}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2004", "line": 220, "column": 0}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 3, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 13, "suggestions": "2005"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "2006"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 15, "suggestions": "2007"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 11, "suggestions": "2008"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 24, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 24, "endColumn": 58}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 33, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 33, "endColumn": 60}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 30, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 30, "endColumn": 58}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 4, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 21, "suggestions": "2009"}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 30, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 30, "endColumn": 60}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 22, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 22, "endColumn": 58}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2010", "line": 388, "column": 0}, {"ruleId": "1850", "severity": 2, "message": "1851", "line": 19, "column": 11, "nodeType": "1852", "messageId": "1853", "endLine": 19, "endColumn": 58}, {"ruleId": "1707", "severity": 1, "message": "1815", "line": 2, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 2, "endColumn": 13, "suggestions": "2011"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 45, "column": 45, "nodeType": "1819", "messageId": "1820", "suggestions": "2012"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 45, "column": 70, "nodeType": "1819", "messageId": "1820", "suggestions": "2013"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 274, "column": 13, "nodeType": "1819", "messageId": "1820", "suggestions": "2014"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 274, "column": 58, "nodeType": "1819", "messageId": "1820", "suggestions": "2015"}, {"ruleId": "1707", "severity": 1, "message": "2016", "line": 544, "column": 25, "nodeType": "1709", "messageId": "1710", "endLine": 544, "endColumn": 36, "suggestions": "2017"}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 7, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 19, "suggestions": "2018"}, {"ruleId": "1707", "severity": 1, "message": "1712", "line": 7, "column": 21, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 33, "suggestions": "2019"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 7, "column": 46, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 54, "suggestions": "2020"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 8, "column": 8, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 21, "suggestions": "2021"}, {"ruleId": "1707", "severity": 1, "message": "2022", "line": 46, "column": 36, "nodeType": "1709", "messageId": "1710", "endLine": 46, "endColumn": 47, "suggestions": "2023"}, {"ruleId": "1707", "severity": 1, "message": "2024", "line": 46, "column": 49, "nodeType": "1709", "messageId": "1710", "endLine": 46, "endColumn": 57, "suggestions": "2025"}, {"ruleId": "1707", "severity": 1, "message": "2022", "line": 46, "column": 36, "nodeType": "1709", "messageId": "1710", "endLine": 46, "endColumn": 47, "suggestions": "2026"}, {"ruleId": "1707", "severity": 1, "message": "2024", "line": 46, "column": 49, "nodeType": "1709", "messageId": "1710", "endLine": 46, "endColumn": 57, "suggestions": "2027"}, {"ruleId": "1707", "severity": 1, "message": "2028", "line": 37, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 37, "endColumn": 10, "suggestions": "2029"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 44, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 44, "endColumn": 17, "suggestions": "2030"}, {"ruleId": "1707", "severity": 1, "message": "2031", "line": 76, "column": 31, "nodeType": "1709", "messageId": "1710", "endLine": 76, "endColumn": 36, "suggestions": "2032"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 79, "column": 15, "nodeType": "1724", "messageId": "1725", "endLine": 79, "endColumn": 27, "suggestions": "2033"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 142, "column": 27, "nodeType": "1819", "messageId": "1820", "suggestions": "2034"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 142, "column": 50, "nodeType": "1819", "messageId": "1820", "suggestions": "2035"}, {"ruleId": "1707", "severity": 1, "message": "2036", "line": 4, "column": 18, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 32, "suggestions": "2037"}, {"ruleId": "1707", "severity": 1, "message": "2038", "line": 4, "column": 34, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 43, "suggestions": "2039"}, {"ruleId": "1707", "severity": 1, "message": "2040", "line": 15, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 15, "endColumn": 12, "suggestions": "2041"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 27, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 27, "endColumn": 17, "suggestions": "2042"}, {"ruleId": "1707", "severity": 1, "message": "2043", "line": 131, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 131, "endColumn": 11, "suggestions": "2044"}, {"ruleId": "1707", "severity": 1, "message": "2028", "line": 29, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 29, "endColumn": 10, "suggestions": "2045"}, {"ruleId": "1707", "severity": 1, "message": "2031", "line": 56, "column": 43, "nodeType": "1709", "messageId": "1710", "endLine": 56, "endColumn": 48, "suggestions": "2046"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 130, "column": 23, "nodeType": "1819", "messageId": "1820", "suggestions": "2047"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 134, "column": 23, "nodeType": "1819", "messageId": "1820", "suggestions": "2048"}, {"ruleId": "1707", "severity": 1, "message": "2049", "line": 201, "column": 39, "nodeType": "1709", "messageId": "1710", "endLine": 201, "endColumn": 52, "suggestions": "2050"}, {"ruleId": "1707", "severity": 1, "message": "2051", "line": 16, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 16, "endColumn": 11, "suggestions": "2052"}, {"ruleId": "1707", "severity": 1, "message": "2053", "line": 3, "column": 38, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 49, "suggestions": "2054"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2055", "line": 133, "column": 0}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2056", "line": 5, "column": 1}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2057", "line": 182, "column": 0}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2058", "line": 7, "column": 9}, {"ruleId": "2059", "severity": 1, "message": "2060", "line": 113, "column": 9, "nodeType": "1849", "endLine": 117, "endColumn": 11}, {"ruleId": "2059", "severity": 1, "message": "2060", "line": 145, "column": 11, "nodeType": "1849", "endLine": 149, "endColumn": 13}, {"ruleId": "1707", "severity": 1, "message": "1978", "line": 3, "column": 44, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 51, "suggestions": "2061"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 6, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 23, "suggestions": "2062"}, {"ruleId": "1707", "severity": 1, "message": "2063", "line": 8, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 17, "suggestions": "2064"}, {"ruleId": "1707", "severity": 1, "message": "2065", "line": 9, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 22, "suggestions": "2066"}, {"ruleId": "1707", "severity": 1, "message": "2067", "line": 24, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 24, "endColumn": 19, "suggestions": "2068"}, {"ruleId": "1707", "severity": 1, "message": "2069", "line": 64, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 64, "endColumn": 29, "suggestions": "2070"}, {"ruleId": "1707", "severity": 1, "message": "2071", "line": 71, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 71, "endColumn": 25, "suggestions": "2072"}, {"ruleId": "1707", "severity": 1, "message": "2073", "line": 78, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 78, "endColumn": 25, "suggestions": "2074"}, {"ruleId": "1707", "severity": 1, "message": "2031", "line": 109, "column": 45, "nodeType": "1709", "messageId": "1710", "endLine": 109, "endColumn": 50, "suggestions": "2075"}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 5, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 23, "suggestions": "2076"}, {"ruleId": "1707", "severity": 1, "message": "2077", "line": 8, "column": 38, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 41, "suggestions": "2078"}, {"ruleId": "1707", "severity": 1, "message": "2079", "line": 8, "column": 43, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 46, "suggestions": "2080"}, {"ruleId": "1707", "severity": 1, "message": "2081", "line": 8, "column": 67, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 72, "suggestions": "2082"}, {"ruleId": "2083", "severity": 2, "message": "2084", "line": 191, "column": 14, "nodeType": "2085", "messageId": "2086", "endLine": 195, "endColumn": 9}, {"ruleId": "2087", "severity": 2, "message": "2088", "line": 221, "column": 14, "nodeType": "2089", "messageId": "2090", "endLine": 221, "endColumn": 28}, {"ruleId": "2087", "severity": 2, "message": "2088", "line": 275, "column": 16, "nodeType": "2089", "messageId": "2090", "endLine": 275, "endColumn": 30}, {"ruleId": "1718", "severity": 1, "message": "2091", "line": 302, "column": 6, "nodeType": "1720", "endLine": 302, "endColumn": 11, "suggestions": "2092"}, {"ruleId": "2087", "severity": 2, "message": "2088", "line": 310, "column": 8, "nodeType": "2089", "messageId": "2090", "endLine": 310, "endColumn": 22}, {"ruleId": "2083", "severity": 2, "message": "2084", "line": 284, "column": 10, "nodeType": "2093", "messageId": "2086", "endLine": 289, "endColumn": 4}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 44, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 44, "endColumn": 22, "suggestions": "2094"}, {"ruleId": "1707", "severity": 1, "message": "2095", "line": 108, "column": 11, "nodeType": "1709", "messageId": "1710", "endLine": 108, "endColumn": 22}, {"ruleId": "1718", "severity": 1, "message": "2096", "line": 190, "column": 6, "nodeType": "1720", "endLine": 190, "endColumn": 16, "suggestions": "2097"}, {"ruleId": "1707", "severity": 1, "message": "2098", "line": 195, "column": 15, "nodeType": "1709", "messageId": "1710", "endLine": 195, "endColumn": 20, "suggestions": "2099"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 300, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 300, "endColumn": 20, "suggestions": "2100"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 96, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 96, "endColumn": 23, "suggestions": "2101"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 124, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 124, "endColumn": 23, "suggestions": "2102"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 128, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 128, "endColumn": 21, "suggestions": "2103"}, {"ruleId": "1707", "severity": 1, "message": "2104", "line": 3, "column": 36, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 44, "suggestions": "2105"}, {"ruleId": "1707", "severity": 1, "message": "2106", "line": 3, "column": 46, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 55, "suggestions": "2107"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2108", "line": 12, "column": 32}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 35, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 35, "endColumn": 20, "suggestions": "2109"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 56, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 56, "endColumn": 22, "suggestions": "2110"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 116, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 116, "endColumn": 20, "suggestions": "2111"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 128, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 128, "endColumn": 20, "suggestions": "2112"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 132, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 132, "endColumn": 20, "suggestions": "2113"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 173, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 173, "endColumn": 22, "suggestions": "2114"}, {"ruleId": "1707", "severity": 1, "message": "2115", "line": 313, "column": 27, "nodeType": "1709", "messageId": "1710", "endLine": 313, "endColumn": 45, "suggestions": "2116"}, {"ruleId": "1707", "severity": 1, "message": "2117", "line": 4, "column": 7, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 17, "suggestions": "2118"}, {"ruleId": "1707", "severity": 1, "message": "2119", "line": 34, "column": 7, "nodeType": "1709", "messageId": "1710", "endLine": 34, "endColumn": 23, "suggestions": "2120"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 52, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 52, "endColumn": 14, "suggestions": "2121"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 62, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 62, "endColumn": 18, "suggestions": "2122"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 70, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 70, "endColumn": 14, "suggestions": "2123"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 83, "column": 15, "nodeType": "1724", "messageId": "1725", "endLine": 83, "endColumn": 26, "suggestions": "2124"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 90, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 90, "endColumn": 20, "suggestions": "2125"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 150, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 150, "endColumn": 18, "suggestions": "2126"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 181, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 181, "endColumn": 18, "suggestions": "2127"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 219, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 219, "endColumn": 18, "suggestions": "2128"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 245, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 245, "endColumn": 18, "suggestions": "2129"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 268, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 268, "endColumn": 18, "suggestions": "2130"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 296, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 296, "endColumn": 14, "suggestions": "2131"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 321, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 321, "endColumn": 20, "suggestions": "2132"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 325, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 325, "endColumn": 18, "suggestions": "2133"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 345, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 345, "endColumn": 20, "suggestions": "2134"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 349, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 349, "endColumn": 18, "suggestions": "2135"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 374, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 374, "endColumn": 14, "suggestions": "2136"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 410, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 410, "endColumn": 14, "suggestions": "2137"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 421, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 421, "endColumn": 14, "suggestions": "2138"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 443, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 443, "endColumn": 22, "suggestions": "2139"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 447, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 447, "endColumn": 16, "suggestions": "2140"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 449, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 449, "endColumn": 18, "suggestions": "2141"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2142", "line": 256, "column": 0}, {"ruleId": "2083", "severity": 2, "message": "2084", "line": 6, "column": 18, "nodeType": "2085", "messageId": "2086", "endLine": 25, "endColumn": 3}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2143", "line": 197, "column": 4}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 65, "column": 9, "nodeType": "1819", "messageId": "1820", "suggestions": "2144"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 65, "column": 31, "nodeType": "1819", "messageId": "1820", "suggestions": "2145"}, {"ruleId": "1707", "severity": 1, "message": "1731", "line": 110, "column": 12, "nodeType": "1709", "messageId": "1710", "endLine": 110, "endColumn": 17}, {"ruleId": "1707", "severity": 1, "message": "2040", "line": 217, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 217, "endColumn": 12, "suggestions": "2146"}, {"ruleId": "1707", "severity": 1, "message": "1862", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 27, "suggestions": "2147"}, {"ruleId": "1707", "severity": 1, "message": "2148", "line": 9, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 24, "suggestions": "2149"}, {"ruleId": "1707", "severity": 1, "message": "2150", "line": 9, "column": 26, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 43, "suggestions": "2151"}, {"ruleId": "1707", "severity": 1, "message": "2152", "line": 10, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 21, "suggestions": "2153"}, {"ruleId": "1707", "severity": 1, "message": "2154", "line": 11, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 21, "suggestions": "2155"}, {"ruleId": "1707", "severity": 1, "message": "2156", "line": 12, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 21, "suggestions": "2157"}, {"ruleId": "1707", "severity": 1, "message": "2158", "line": 18, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 18, "endColumn": 20, "suggestions": "2159"}, {"ruleId": "1707", "severity": 1, "message": "2160", "line": 315, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 315, "endColumn": 31, "suggestions": "2161"}, {"ruleId": "1707", "severity": 1, "message": "2162", "line": 391, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 391, "endColumn": 23, "suggestions": "2163"}, {"ruleId": "1718", "severity": 1, "message": "2164", "line": 500, "column": 6, "nodeType": "1720", "endLine": 500, "endColumn": 8, "suggestions": "2165"}, {"ruleId": "1707", "severity": 1, "message": "2166", "line": 506, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 506, "endColumn": 30, "suggestions": "2167"}, {"ruleId": "1707", "severity": 1, "message": "2168", "line": 63, "column": 16, "nodeType": "1709", "messageId": "1710", "endLine": 63, "endColumn": 17}, {"ruleId": "2169", "severity": 1, "message": "2170", "line": 449, "column": 7, "nodeType": "1849", "endLine": 454, "endColumn": 9}, {"ruleId": "1718", "severity": 1, "message": "2171", "line": 79, "column": 6, "nodeType": "1720", "endLine": 79, "endColumn": 58, "suggestions": "2172"}, {"ruleId": "1707", "severity": 1, "message": "2173", "line": 19, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 19, "endColumn": 12, "suggestions": "2174"}, {"ruleId": "1707", "severity": 1, "message": "2031", "line": 108, "column": 34, "nodeType": "1709", "messageId": "1710", "endLine": 108, "endColumn": 39, "suggestions": "2175"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2176", "line": 175, "column": 0}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 60, "column": 21, "nodeType": "1819", "messageId": "1820", "suggestions": "2177"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 64, "column": 21, "nodeType": "1819", "messageId": "1820", "suggestions": "2178"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 106, "column": 27, "nodeType": "1819", "messageId": "1820", "suggestions": "2179"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 110, "column": 27, "nodeType": "1819", "messageId": "1820", "suggestions": "2180"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2181", "line": 7, "column": 42}, {"ruleId": "1707", "severity": 1, "message": "1716", "line": 13, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 13, "endColumn": 23, "suggestions": "2182"}, {"ruleId": "1707", "severity": 1, "message": "2183", "line": 28, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 28, "endColumn": 15, "suggestions": "2184"}, {"ruleId": "1718", "severity": 1, "message": "2185", "line": 128, "column": 6, "nodeType": "1720", "endLine": 128, "endColumn": 32, "suggestions": "2186"}, {"ruleId": "1718", "severity": 1, "message": "2187", "line": 132, "column": 6, "nodeType": "1720", "endLine": 132, "endColumn": 33, "suggestions": "2188"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 152, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 152, "endColumn": 20, "suggestions": "2189"}, {"ruleId": "1707", "severity": 1, "message": "2190", "line": 31, "column": 5, "nodeType": "1709", "messageId": "1710", "endLine": 31, "endColumn": 12, "suggestions": "2191"}, {"ruleId": "1707", "severity": 1, "message": "2192", "line": 32, "column": 5, "nodeType": "1709", "messageId": "1710", "endLine": 32, "endColumn": 13, "suggestions": "2193"}, {"ruleId": "1707", "severity": 1, "message": "2194", "line": 33, "column": 5, "nodeType": "1709", "messageId": "1710", "endLine": 33, "endColumn": 12, "suggestions": "2195"}, {"ruleId": "2087", "severity": 2, "message": "2196", "line": 160, "column": 20, "nodeType": "2089", "messageId": "2090", "endLine": 160, "endColumn": 36}, {"ruleId": "2087", "severity": 2, "message": "2197", "line": 168, "column": 18, "nodeType": "2089", "messageId": "2090", "endLine": 168, "endColumn": 36}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2198", "line": 5, "column": 9}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2199", "line": 13, "column": 32}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2199", "line": 13, "column": 32}, {"ruleId": "1707", "severity": 1, "message": "1708", "line": 4, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 12, "suggestions": "2200"}, {"ruleId": "1707", "severity": 1, "message": "1836", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 12, "suggestions": "2201"}, {"ruleId": "1707", "severity": 1, "message": "1714", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 11, "suggestions": "2202"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 159, "column": 13, "nodeType": "1819", "messageId": "1820", "suggestions": "2203"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 159, "column": 21, "nodeType": "1819", "messageId": "1820", "suggestions": "2204"}, {"ruleId": "2205", "severity": 1, "message": "2206", "line": 331, "column": 1, "nodeType": "1887", "endLine": 343, "endColumn": 3}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 44, "column": 9, "nodeType": "1819", "messageId": "1820", "suggestions": "2207"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 50, "column": 11, "nodeType": "1819", "messageId": "1820", "suggestions": "2208"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 50, "column": 19, "nodeType": "1819", "messageId": "1820", "suggestions": "2209"}, {"ruleId": "1707", "severity": 1, "message": "2210", "line": 26, "column": 32, "nodeType": "1709", "messageId": "1710", "endLine": 26, "endColumn": 36, "suggestions": "2211"}, {"ruleId": "1707", "severity": 1, "message": "1992", "line": 84, "column": 35, "nodeType": "1709", "messageId": "1710", "endLine": 84, "endColumn": 40, "suggestions": "2212"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 96, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 96, "endColumn": 20, "suggestions": "2213"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 201, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 201, "endColumn": 17, "suggestions": "2214"}, {"ruleId": "1707", "severity": 1, "message": "2215", "line": 36, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 36, "endColumn": 10, "suggestions": "2216"}, {"ruleId": "1707", "severity": 1, "message": "2217", "line": 39, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 39, "endColumn": 8, "suggestions": "2218"}, {"ruleId": "2219", "severity": 1, "message": "2220", "line": 141, "column": 9, "nodeType": "1849", "endLine": 160, "endColumn": 11}, {"ruleId": "1707", "severity": 1, "message": "2221", "line": 22, "column": 11, "nodeType": "1709", "messageId": "1710", "endLine": 22, "endColumn": 20, "suggestions": "2222"}, {"ruleId": "1707", "severity": 1, "message": "2223", "line": 27, "column": 13, "nodeType": "1709", "messageId": "1710", "endLine": 27, "endColumn": 20, "suggestions": "2224"}, {"ruleId": "1707", "severity": 1, "message": "2081", "line": 28, "column": 16, "nodeType": "1709", "messageId": "1710", "endLine": 28, "endColumn": 21, "suggestions": "2225"}, {"ruleId": "1707", "severity": 1, "message": "2226", "line": 32, "column": 15, "nodeType": "1709", "messageId": "1710", "endLine": 32, "endColumn": 20, "suggestions": "2227"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2228", "line": 181, "column": 2}, {"ruleId": "1707", "severity": 1, "message": "2229", "line": 82, "column": 13, "nodeType": "1709", "messageId": "1710", "endLine": 82, "endColumn": 24, "suggestions": "2230"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2231", "line": 6, "column": 9}, {"ruleId": "1707", "severity": 1, "message": "2232", "line": 26, "column": 9, "nodeType": "1709", "messageId": "1710", "endLine": 26, "endColumn": 18, "suggestions": "2233"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 66, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 66, "endColumn": 17, "suggestions": "2234"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 205, "column": 9, "nodeType": "1819", "messageId": "1820", "suggestions": "2235"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 208, "column": 73, "nodeType": "1819", "messageId": "1820", "suggestions": "2236"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2237", "line": 13, "column": 47}, {"ruleId": "1707", "severity": 1, "message": "2238", "line": 12, "column": 10, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 19, "suggestions": "2239"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2240", "line": 201, "column": 8}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2241", "line": 238, "column": 10}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2242", "line": 348, "column": 12}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2243", "line": 11, "column": 70}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2244", "line": 135, "column": 12}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 57, "column": 13, "nodeType": "1724", "messageId": "1725", "endLine": 57, "endColumn": 25, "suggestions": "2245"}, {"ruleId": "1817", "severity": 1, "message": "1818", "line": 175, "column": 58, "nodeType": "1819", "messageId": "1820", "suggestions": "2246"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2247", "line": 155, "column": 10}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2248", "line": 263, "column": 14}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "2249", "line": 290, "column": 14}, {"ruleId": "1707", "severity": 1, "message": "2250", "line": 2, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 2, "endColumn": 11, "suggestions": "2251"}, {"ruleId": "1707", "severity": 1, "message": "2252", "line": 3, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 7, "suggestions": "2253"}, {"ruleId": "1707", "severity": 1, "message": "2254", "line": 4, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 6, "suggestions": "2255"}, {"ruleId": "1707", "severity": 1, "message": "2256", "line": 5, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 10, "suggestions": "2257"}, {"ruleId": "1707", "severity": 1, "message": "2258", "line": 6, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 6, "endColumn": 10, "suggestions": "2259"}, {"ruleId": "1707", "severity": 1, "message": "2260", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 9, "suggestions": "2261"}, {"ruleId": "1707", "severity": 1, "message": "2262", "line": 8, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 11, "suggestions": "2263"}, {"ruleId": "1707", "severity": 1, "message": "2264", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 10, "suggestions": "2265"}, {"ruleId": "1707", "severity": 1, "message": "2266", "line": 10, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 10, "endColumn": 8, "suggestions": "2267"}, {"ruleId": "1707", "severity": 1, "message": "2268", "line": 11, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 9, "suggestions": "2269"}, {"ruleId": "1707", "severity": 1, "message": "2270", "line": 12, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 11, "suggestions": "2271"}, {"ruleId": "1707", "severity": 1, "message": "2250", "line": 2, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 2, "endColumn": 11, "suggestions": "2272"}, {"ruleId": "1707", "severity": 1, "message": "2252", "line": 3, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 3, "endColumn": 7, "suggestions": "2273"}, {"ruleId": "1707", "severity": 1, "message": "2254", "line": 4, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 4, "endColumn": 6, "suggestions": "2274"}, {"ruleId": "1707", "severity": 1, "message": "2256", "line": 5, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 5, "endColumn": 10, "suggestions": "2275"}, {"ruleId": "1707", "severity": 1, "message": "2260", "line": 7, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 7, "endColumn": 9, "suggestions": "2276"}, {"ruleId": "1707", "severity": 1, "message": "2262", "line": 8, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 8, "endColumn": 11, "suggestions": "2277"}, {"ruleId": "1707", "severity": 1, "message": "2264", "line": 9, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 9, "endColumn": 10, "suggestions": "2278"}, {"ruleId": "1707", "severity": 1, "message": "2268", "line": 11, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 11, "endColumn": 9, "suggestions": "2279"}, {"ruleId": "1707", "severity": 1, "message": "2270", "line": 12, "column": 3, "nodeType": "1709", "messageId": "1710", "endLine": 12, "endColumn": 11, "suggestions": "2280"}, {"ruleId": "1707", "severity": 1, "message": "2281", "line": 151, "column": 18, "nodeType": "1709", "messageId": "1710", "endLine": 151, "endColumn": 27, "suggestions": "2282"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 210, "column": 11, "nodeType": "1724", "messageId": "1725", "endLine": 210, "endColumn": 24, "suggestions": "2283"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 197, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 197, "endColumn": 16, "suggestions": "2284"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 239, "column": 3, "nodeType": "1724", "messageId": "1725", "endLine": 239, "endColumn": 16, "suggestions": "2285"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 357, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 357, "endColumn": 17, "suggestions": "2286"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 379, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 379, "endColumn": 18, "suggestions": "2287"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 404, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 404, "endColumn": 19, "suggestions": "2288"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 407, "column": 5, "nodeType": "1724", "messageId": "1725", "endLine": 407, "endColumn": 16, "suggestions": "2289"}, {"ruleId": "1707", "severity": 1, "message": "2290", "line": 280, "column": 26, "nodeType": "1709", "messageId": "1710", "endLine": 280, "endColumn": 33, "suggestions": "2291"}, {"ruleId": "1707", "severity": 1, "message": "2292", "line": 293, "column": 37, "nodeType": "1709", "messageId": "1710", "endLine": 293, "endColumn": 41, "suggestions": "2293"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 44, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 44, "endColumn": 18, "suggestions": "2294"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 47, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 47, "endColumn": 18, "suggestions": "2295"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 64, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 64, "endColumn": 20, "suggestions": "2296"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 74, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 74, "endColumn": 18, "suggestions": "2297"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 81, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 81, "endColumn": 20, "suggestions": "2298"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 98, "column": 9, "nodeType": "1724", "messageId": "1725", "endLine": 98, "endColumn": 20, "suggestions": "2299"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 107, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 107, "endColumn": 20, "suggestions": "2300"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 131, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 131, "endColumn": 18, "suggestions": "2301"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 133, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 133, "endColumn": 20, "suggestions": "2302"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 155, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 155, "endColumn": 18, "suggestions": "2303"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 157, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 157, "endColumn": 20, "suggestions": "2304"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 179, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 179, "endColumn": 20, "suggestions": "2305"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 198, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 198, "endColumn": 18, "suggestions": "2306"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 200, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 200, "endColumn": 20, "suggestions": "2307"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 208, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 208, "endColumn": 19, "suggestions": "2308"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 295, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 295, "endColumn": 18, "suggestions": "2309"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 298, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 298, "endColumn": 20, "suggestions": "2310"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 341, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 341, "endColumn": 20, "suggestions": "2311"}, {"ruleId": "1722", "severity": 1, "message": "1723", "line": 350, "column": 7, "nodeType": "1724", "messageId": "1725", "endLine": 350, "endColumn": 20, "suggestions": "2312"}, "no-unused-vars", "'HeroTitle' is defined but never used.", "Identifier", "unusedVar", ["2313"], "'SectionTitle' is defined but never used.", ["2314"], "'BodyText' is defined but never used.", ["2315"], "'UnifiedButton' is defined but never used.", ["2316"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'verifyTokenAndLoadData'. Either include it or remove the dependency array.", "ArrayExpression", ["2317"], "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["2318"], ["2319"], ["2320"], ["2321"], ["2322"], "'error' is defined but never used.", ["2323"], ["2324"], ["2325"], ["2326"], ["2327"], ["2328"], "'crypto' is defined but never used.", ["2329"], ["2330"], ["2331"], ["2332"], ["2333"], ["2334"], ["2335"], ["2336"], ["2337"], ["2338"], ["2339"], "'hashPassword' is defined but never used.", ["2340"], ["2341"], ["2342"], "'validateSchema' is defined but never used.", ["2343"], "'ValidationError' is defined but never used.", ["2344"], "'handleApiError' is defined but never used.", ["2345"], "'bookingSchema' is assigned a value but never used.", ["2346"], "'emailSent' is assigned a value but never used.", ["2347"], ["2348"], "'emailContent' is assigned a value but never used.", ["2349"], ["2350"], ["2351"], ["2352"], ["2353"], ["2354"], "'amount_paid' is assigned a value but never used.", ["2355"], ["2356"], ["2357"], ["2358"], "'payment_method' is assigned a value but never used.", ["2359"], ["2360"], ["2361"], ["2362"], ["2363"], ["2364"], ["2365"], ["2366"], ["2367"], ["2368"], ["2369"], ["2370"], ["2371"], ["2372"], ["2373"], ["2374"], ["2375"], ["2376"], ["2377"], ["2378"], ["2379"], ["2380"], ["2381"], ["2382"], ["2383"], ["2384"], ["2385"], ["2386"], ["2387"], ["2388"], ["2389"], ["2390"], "'request' is defined but never used. Allowed unused args must match /^_/u.", ["2391"], ["2392"], ["2393"], ["2394"], "'Image' is defined but never used.", ["2395"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["2396", "2397", "2398", "2399"], ["2400", "2401", "2402", "2403"], ["2404"], "'Link' is defined but never used.", ["2405"], ["2406"], ["2407"], "'Icon' is defined but never used.", ["2408"], "'FeatureList' is defined but never used.", ["2409"], ["2410"], ["2411"], ["2412"], ["2413"], "'CardTitle' is defined but never used.", ["2414"], ["2415"], ["2416"], ["2417"], ["2418"], ["2419"], "'loggingError' is defined but never used.", ["2420"], ["2421"], ["2422"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/program/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", ["2423", "2424", "2425", "2426"], ["2427", "2428", "2429", "2430"], ["2431"], ["2432"], ["2433", "2434", "2435", "2436"], ["2437", "2438", "2439", "2440"], ["2441", "2442", "2443", "2444"], ["2445", "2446", "2447", "2448"], "'generateAdvancedMetadata' is defined but never used.", ["2449"], ["2450"], ["2451", "2452", "2453", "2454"], ["2455", "2456", "2457", "2458"], ["2459", "2460", "2461", "2462"], ["2463", "2464", "2465", "2466"], ["2467", "2468", "2469", "2470"], ["2471", "2472", "2473", "2474"], ["2475"], "'isDesktop' is assigned a value but never used.", ["2476"], "'isTablet' is assigned a value but never used.", ["2477"], "'UnifiedCard' is defined but never used.", ["2478"], "'TestimonialCard' is defined but never used.", ["2479"], "'SecondaryButton' is defined but never used.", ["2480"], ["2481"], ["2482", "2483", "2484", "2485"], ["2486", "2487", "2488", "2489"], "@next/next/no-async-client-component", "Prevent Client Components from being async functions. See: https://nextjs.org/docs/messages/no-async-client-component", "ExportDefaultDeclaration", ["2490"], ["2491"], ["2492"], ["2493"], "'destination' is assigned a value but never used.", ["2494"], ["2495"], ["2496"], ["2497"], "'generateRetreatStructuredData' is defined but never used.", ["2498"], "'destinations' is defined but never used.", ["2499"], ["2500", "2501", "2502", "2503"], ["2504", "2505", "2506", "2507"], ["2508", "2509", "2510", "2511"], ["2512", "2513", "2514", "2515"], ["2516"], ["2517"], ["2518", "2519", "2520", "2521"], ["2522", "2523", "2524", "2525"], ["2526"], ["2527"], ["2528"], ["2529"], "'heroStyles' is assigned a value but never used.", ["2530"], ["2531"], ["2532"], "'CTAButton' is defined but never used.", ["2533"], "'FitsseyWidget' is defined but never used.", ["2534"], "'structuredData' is assigned a value but never used.", ["2535"], "React Hook useEffect has a missing dependency: 'toggleHighContrast'. Either include it or remove the dependency array.", ["2536"], "Parsing error: Unexpected token (38:0)", "React Hook useEffect has a missing dependency: 'ENTERPRISE_TESTS'. Either include it or remove the dependency array.", ["2537"], "'assignment' is defined but never used. Allowed unused args must match /^_/u.", ["2538"], "React Hook useEffect has a missing dependency: 'trackConversion'. Either include it or remove the dependency array.", ["2539"], "React Hook useEffect has a missing dependency: 'analyzeTestResults'. Either include it or remove the dependency array.", ["2540"], "Parsing error: Unexpected token (227:0)", "'conversionData' is assigned a value but never used.", ["2541"], "'key' is defined but never used. Allowed unused args must match /^_/u.", ["2542"], "'e' is defined but never used. Allowed unused args must match /^_/u.", ["2543"], ["2544"], ["2545"], ["2546"], ["2547"], ["2548"], ["2549"], ["2550"], ["2551"], ["2552"], ["2553"], ["2554"], ["2555"], "Parsing error: Unexpected token (790:0)", ["2556"], "'endTime' is assigned a value but never used.", ["2557"], ["2558"], ["2559"], "'SEARCH_INTENT_MAPPING' is assigned a value but never used.", ["2560"], "React Hook useEffect has missing dependencies: 'TRACKED_KEYWORDS' and 'trackEvent'. Either include them or remove the dependency array.", ["2561"], "'competitor' is defined but never used. Allowed unused args must match /^_/u.", ["2562"], "React Hook useEffect has missing dependencies: 'COMPETITOR_TRACKING' and 'TRACKED_KEYWORDS'. Either include them or remove the dependency array.", ["2563"], ["2564"], "React Hook useEffect has a missing dependency: 'trackEvent'. Either include it or remove the dependency array.", ["2565"], ["2566"], "Parsing error: Unexpected token (438:0)", ["2567"], ["2568"], ["2569"], "'router' is assigned a value but never used.", ["2570"], "Parsing error: Missing semicolon. (14:32)", "'useMemo' is defined but never used.", ["2571"], ["2572"], ["2573"], ["2574"], ["2575"], ["2576"], "'showBookingForm' is assigned a value but never used.", ["2577"], "Parsing error: Unexpected token (528:70)", ["2578"], "'pathname' is assigned a value but never used.", ["2579"], ["2580"], "'error' is defined but never used. Allowed unused args must match /^_/u.", ["2581"], ["2582"], ["2583"], ["2584"], "'errorInfo' is defined but never used. Allowed unused args must match /^_/u.", ["2585"], ["2586"], ["2587"], "Parsing error: Unexpected token (107:1)", ["2588"], "Parsing error: Identifier 'Icon' has already been declared. (6:9)", "Parsing error: Unexpected token (220:0)", ["2589"], ["2590"], ["2591"], ["2592"], ["2593"], "Parsing error: Unexpected token (388:0)", ["2594"], ["2595", "2596", "2597", "2598"], ["2599", "2600", "2601", "2602"], ["2603", "2604", "2605", "2606"], ["2607", "2608", "2609", "2610"], "'latestPosts' is defined but never used. Allowed unused args must match /^_/u.", ["2611"], ["2612"], ["2613"], ["2614"], ["2615"], "'currentPost' is defined but never used. Allowed unused args must match /^_/u.", ["2616"], "'allPosts' is defined but never used. Allowed unused args must match /^_/u.", ["2617"], ["2618"], ["2619"], "'variant' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2620"], ["2621"], "'index' is defined but never used. Allowed unused args must match /^_/u.", ["2622"], ["2623"], ["2624", "2625", "2626", "2627"], ["2628", "2629", "2630", "2631"], "'useMotionValue' is defined but never used.", ["2632"], "'useSpring' is defined but never used.", ["2633"], "'intensity' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2634"], ["2635"], "'animated' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2636"], ["2637"], ["2638"], ["2639", "2640", "2641", "2642"], ["2643", "2644", "2645", "2646"], "'testimonialId' is defined but never used. Allowed unused args must match /^_/u.", ["2647"], "'position' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2648"], "'useCallback' is defined but never used.", ["2649"], "Parsing error: Unexpected token (133:0)", "Parsing error: Unexpected token (5:1)", "Parsing error: Unexpected token (182:0)", "Parsing error: Identifier 'UnifiedButton' has already been declared. (7:9)", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["2650"], ["2651"], "'NavLink' is defined but never used.", ["2652"], "'mainNavItems' is defined but never used.", ["2653"], "'isVisible' is assigned a value but never used.", ["2654"], "'handleDropdownToggle' is assigned a value but never used.", ["2655"], "'handleMouseEnter' is assigned a value but never used.", ["2656"], "'handleMouseLeave' is assigned a value but never used.", ["2657"], ["2658"], ["2659"], "'src' is defined but never used. Allowed unused args must match /^_/u.", ["2660"], "'alt' is defined but never used. Allowed unused args must match /^_/u.", ["2661"], "'props' is defined but never used. Allowed unused args must match /^_/u.", ["2662"], "react/display-name", "Component definition is missing display name", "CallExpression", "noDisplayName", "react/jsx-no-undef", "'ImageOptimizer' is not defined.", "JSXIdentifier", "undefined", "React Hook useEffect has an unnecessary dependency: 'src'. Either exclude it or remove the dependency array. Outer scope values like 'src' aren't valid dependencies because mutating them doesn't re-render the component.", ["2663"], "ArrowFunctionExpression", ["2664"], "'scrollDepth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'trackMetric'. Either include it or remove the dependency array.", ["2665"], "'event' is defined but never used. Allowed unused args must match /^_/u.", ["2666"], ["2667"], ["2668"], ["2669"], ["2670"], "'useState' is defined but never used.", ["2671"], "'useEffect' is defined but never used.", ["2672"], "Parsing error: Missing semicolon. (12:32)", ["2673"], ["2674"], ["2675"], ["2676"], ["2677"], ["2678"], "'setUpdateAvailable' is assigned a value but never used.", ["2679"], "'CACHE_NAME' is assigned a value but never used.", ["2680"], "'CACHE_STRATEGIES' is assigned a value but never used.", ["2681"], ["2682"], ["2683"], ["2684"], ["2685"], ["2686"], ["2687"], ["2688"], ["2689"], ["2690"], ["2691"], ["2692"], ["2693"], ["2694"], ["2695"], ["2696"], ["2697"], ["2698"], ["2699"], ["2700"], ["2701"], ["2702"], "Parsing error: Unexpected token (256:0)", "Parsing error: Expected corresponding JSX closing tag for <article>. (197:4)", ["2703", "2704", "2705", "2706"], ["2707", "2708", "2709", "2710"], ["2711"], ["2712"], "'competitorData' is assigned a value but never used.", ["2713"], "'setCompetitorData' is assigned a value but never used.", ["2714"], "'rankingData' is assigned a value but never used.", ["2715"], "'keywordGaps' is assigned a value but never used.", ["2716"], "'contentGaps' is assigned a value but never used.", ["2717"], "'COMPETITORS' is assigned a value but never used.", ["2718"], "'BACKLINK_OPPORTUNITIES' is assigned a value but never used.", ["2719"], "'TECHNICAL_GAPS' is assigned a value but never used.", ["2720"], "React Hook useEffect has missing dependencies: 'CONTENT_OPPORTUNITIES.missingPages' and 'KEYWORD_OPPORTUNITIES.highVolumeLowCompetition'. Either include them or remove the dependency array.", ["2721"], "'DOMINATION_STRATEGIES' is assigned a value but never used.", ["2722"], "'e' is defined but never used.", "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "React Hook useEffect has missing dependencies: 'siteDescription', 'siteImage', 'siteName', and 'siteUrl'. Either include them or remove the dependency array.", ["2723"], "'className' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2724"], ["2725"], "Parsing error: Unexpected token (175:0)", ["2726", "2727", "2728", "2729"], ["2730", "2731", "2732", "2733"], ["2734", "2735", "2736", "2737"], ["2738", "2739", "2740", "2741"], "Parsing error: Unexpected token, expected \",\" (7:42)", ["2742"], "'rates' is assigned a value but never used.", ["2743"], "React Hook useEffect has a missing dependency: 'fetchExchangeRates'. Either include it or remove the dependency array.", ["2744"], "React Hook useEffect has a missing dependency: 'calculateConversion'. Either include it or remove the dependency array.", ["2745"], ["2746"], "'climate' is assigned a value but never used.", ["2747"], "'bestTime' is assigned a value but never used.", ["2748"], "'culture' is assigned a value but never used.", ["2749"], "'CalendarDaysIcon' is not defined.", "'CurrencyDollarIcon' is not defined.", "Parsing error: Identifier 'Icon' has already been declared. (5:9)", "Parsing error: Missing semicolon. (13:32)", ["2750"], ["2751"], ["2752"], ["2753", "2754", "2755", "2756"], ["2757", "2758", "2759", "2760"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", ["2761", "2762", "2763", "2764"], ["2765", "2766", "2767", "2768"], ["2769", "2770", "2771", "2772"], "'type' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2773"], ["2774"], ["2775"], ["2776"], "'quality' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2777"], "'sizes' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2778"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'direction' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2779"], "'onClick' is defined but never used. Allowed unused args must match /^_/u.", ["2780"], ["2781"], "'scale' is assigned a value but never used. Allowed unused args must match /^_/u.", ["2782"], "Parsing error: Unexpected token (181:2)", "'breakpoints' is assigned a value but never used.", ["2783"], "Parsing error: Identifier 'UnifiedButton' has already been declared. (6:9)", "'scrollRef' is assigned a value but never used.", ["2784"], ["2785"], ["2786", "2787", "2788", "2789"], ["2790", "2791", "2792", "2793"], "Parsing error: Unexpected token, expected \",\" (13:47)", "'timeOfDay' is assigned a value but never used.", ["2794"], "Parsing error: Unexpected token (201:8)", "Parsing error: Unexpected token (238:10)", "Parsing error: Unexpected token (348:12)", "Parsing error: Unexpected token, expected \",\" (11:70)", "Parsing error: Unexpected token (135:12)", ["2795"], ["2796", "2797", "2798", "2799"], "Parsing error: Unexpected token (155:10)", "Parsing error: Unexpected token (263:14)", "Parsing error: Unexpected token (290:14)", "'Calendar' is defined but never used.", ["2800"], "'User' is defined but never used.", ["2801"], "'Tag' is defined but never used.", ["2802"], "'Palette' is defined but never used.", ["2803"], "'Sunrise' is defined but never used.", ["2804"], "'Sunset' is defined but never used.", ["2805"], "'Mountain' is defined but never used.", ["2806"], "'Droplet' is defined but never used.", ["2807"], "'Heart' is defined but never used.", ["2808"], "'MapPin' is defined but never used.", ["2809"], "'Sparkles' is defined but never used.", ["2810"], ["2811"], ["2812"], ["2813"], ["2814"], ["2815"], ["2816"], ["2817"], ["2818"], ["2819"], "'setOffset' is assigned a value but never used.", ["2820"], ["2821"], ["2822"], ["2823"], ["2824"], ["2825"], ["2826"], ["2827"], "'keyword' is defined but never used. Allowed unused args must match /^_/u.", ["2828"], "'type' is defined but never used. Allowed unused args must match /^_/u.", ["2829"], ["2830"], ["2831"], ["2832"], ["2833"], ["2834"], ["2835"], ["2836"], ["2837"], ["2838"], ["2839"], ["2840"], ["2841"], ["2842"], ["2843"], ["2844"], ["2845"], ["2846"], ["2847"], ["2848"], {"messageId": "2849", "data": "2850", "fix": "2851", "desc": "2852"}, {"messageId": "2849", "data": "2853", "fix": "2854", "desc": "2855"}, {"messageId": "2849", "data": "2856", "fix": "2857", "desc": "2858"}, {"messageId": "2849", "data": "2859", "fix": "2860", "desc": "2861"}, {"desc": "2862", "fix": "2863"}, {"fix": "2864", "messageId": "2865", "data": "2866", "desc": "2867"}, {"fix": "2868", "messageId": "2865", "data": "2869", "desc": "2867"}, {"fix": "2870", "messageId": "2865", "data": "2871", "desc": "2867"}, {"messageId": "2849", "data": "2872", "fix": "2873", "desc": "2861"}, {"fix": "2874", "messageId": "2865", "data": "2875", "desc": "2867"}, {"fix": "2876", "messageId": "2865", "data": "2877", "desc": "2867"}, {"fix": "2878", "messageId": "2865", "data": "2879", "desc": "2880"}, {"fix": "2881", "messageId": "2865", "data": "2882", "desc": "2867"}, {"fix": "2883", "messageId": "2865", "data": "2884", "desc": "2867"}, {"fix": "2885", "messageId": "2865", "data": "2886", "desc": "2867"}, {"fix": "2887", "messageId": "2865", "data": "2888", "desc": "2867"}, {"messageId": "2849", "data": "2889", "fix": "2890", "desc": "2891"}, {"fix": "2892", "messageId": "2865", "data": "2893", "desc": "2894"}, {"fix": "2895", "messageId": "2865", "data": "2896", "desc": "2894"}, {"fix": "2897", "messageId": "2865", "data": "2898", "desc": "2880"}, {"fix": "2899", "messageId": "2865", "data": "2900", "desc": "2894"}, {"fix": "2901", "messageId": "2865", "data": "2902", "desc": "2894"}, {"fix": "2903", "messageId": "2865", "data": "2904", "desc": "2894"}, {"fix": "2905", "messageId": "2865", "data": "2906", "desc": "2894"}, {"fix": "2907", "messageId": "2865", "data": "2908", "desc": "2894"}, {"fix": "2909", "messageId": "2865", "data": "2910", "desc": "2867"}, {"fix": "2911", "messageId": "2865", "data": "2912", "desc": "2867"}, {"messageId": "2849", "data": "2913", "fix": "2914", "desc": "2915"}, {"fix": "2916", "messageId": "2865", "data": "2917", "desc": "2894"}, {"fix": "2918", "messageId": "2865", "data": "2919", "desc": "2867"}, {"messageId": "2849", "data": "2920", "fix": "2921", "desc": "2922"}, {"messageId": "2849", "data": "2923", "fix": "2924", "desc": "2925"}, {"messageId": "2849", "data": "2926", "fix": "2927", "desc": "2928"}, {"fix": "2929", "messageId": "2865", "data": "2930", "desc": "2894"}, {"messageId": "2849", "data": "2931", "fix": "2932", "desc": "2933"}, {"fix": "2934", "messageId": "2865", "data": "2935", "desc": "2894"}, {"messageId": "2849", "data": "2936", "fix": "2937", "desc": "2938"}, {"fix": "2939", "messageId": "2865", "data": "2940", "desc": "2894"}, {"fix": "2941", "messageId": "2865", "data": "2942", "desc": "2867"}, {"fix": "2943", "messageId": "2865", "data": "2944", "desc": "2880"}, {"fix": "2945", "messageId": "2865", "data": "2946", "desc": "2880"}, {"fix": "2947", "messageId": "2865", "data": "2948", "desc": "2867"}, {"messageId": "2849", "data": "2949", "fix": "2950", "desc": "2951"}, {"fix": "2952", "messageId": "2865", "data": "2953", "desc": "2880"}, {"fix": "2954", "messageId": "2865", "data": "2955", "desc": "2867"}, {"fix": "2956", "messageId": "2865", "data": "2957", "desc": "2880"}, {"messageId": "2849", "data": "2958", "fix": "2959", "desc": "2960"}, {"fix": "2961", "messageId": "2865", "data": "2962", "desc": "2880"}, {"fix": "2963", "messageId": "2865", "data": "2964", "desc": "2880"}, {"messageId": "2849", "data": "2965", "fix": "2966", "desc": "2938"}, {"fix": "2967", "messageId": "2865", "data": "2968", "desc": "2880"}, {"fix": "2969", "messageId": "2865", "data": "2970", "desc": "2867"}, {"fix": "2971", "messageId": "2865", "data": "2972", "desc": "2894"}, {"fix": "2973", "messageId": "2865", "data": "2974", "desc": "2867"}, {"fix": "2975", "messageId": "2865", "data": "2976", "desc": "2880"}, {"fix": "2977", "messageId": "2865", "data": "2978", "desc": "2867"}, {"fix": "2979", "messageId": "2865", "data": "2980", "desc": "2880"}, {"fix": "2981", "messageId": "2865", "data": "2982", "desc": "2867"}, {"fix": "2983", "messageId": "2865", "data": "2984", "desc": "2880"}, {"fix": "2985", "messageId": "2865", "data": "2986", "desc": "2867"}, {"fix": "2987", "messageId": "2865", "data": "2988", "desc": "2880"}, {"fix": "2989", "messageId": "2865", "data": "2990", "desc": "2867"}, {"fix": "2991", "messageId": "2865", "data": "2992", "desc": "2867"}, {"fix": "2993", "messageId": "2865", "data": "2994", "desc": "2867"}, {"fix": "2995", "messageId": "2865", "data": "2996", "desc": "2867"}, {"fix": "2997", "messageId": "2865", "data": "2998", "desc": "2867"}, {"fix": "2999", "messageId": "2865", "data": "3000", "desc": "2880"}, {"fix": "3001", "messageId": "2865", "data": "3002", "desc": "2867"}, {"fix": "3003", "messageId": "2865", "data": "3004", "desc": "2867"}, {"fix": "3005", "messageId": "2865", "data": "3006", "desc": "2880"}, {"fix": "3007", "messageId": "2865", "data": "3008", "desc": "2880"}, {"fix": "3009", "messageId": "2865", "data": "3010", "desc": "2880"}, {"fix": "3011", "messageId": "2865", "data": "3012", "desc": "2867"}, {"fix": "3013", "messageId": "2865", "data": "3014", "desc": "2867"}, {"fix": "3015", "messageId": "2865", "data": "3016", "desc": "2880"}, {"fix": "3017", "messageId": "2865", "data": "3018", "desc": "2867"}, {"fix": "3019", "messageId": "2865", "data": "3020", "desc": "2867"}, {"fix": "3021", "messageId": "2865", "data": "3022", "desc": "2867"}, {"messageId": "2849", "data": "3023", "fix": "3024", "desc": "3025"}, {"fix": "3026", "messageId": "2865", "data": "3027", "desc": "2880"}, {"fix": "3028", "messageId": "2865", "data": "3029", "desc": "2880"}, {"fix": "3030", "messageId": "2865", "data": "3031", "desc": "2867"}, {"messageId": "2849", "data": "3032", "fix": "3033", "desc": "3034"}, {"messageId": "3035", "data": "3036", "fix": "3037", "desc": "3038"}, {"messageId": "3035", "data": "3039", "fix": "3040", "desc": "3041"}, {"messageId": "3035", "data": "3042", "fix": "3043", "desc": "3044"}, {"messageId": "3035", "data": "3045", "fix": "3046", "desc": "3047"}, {"messageId": "3035", "data": "3048", "fix": "3049", "desc": "3038"}, {"messageId": "3035", "data": "3050", "fix": "3051", "desc": "3041"}, {"messageId": "3035", "data": "3052", "fix": "3053", "desc": "3044"}, {"messageId": "3035", "data": "3054", "fix": "3055", "desc": "3047"}, {"messageId": "2849", "data": "3056", "fix": "3057", "desc": "3034"}, {"messageId": "2849", "data": "3058", "fix": "3059", "desc": "3060"}, {"messageId": "2849", "data": "3061", "fix": "3062", "desc": "2852"}, {"messageId": "2849", "data": "3063", "fix": "3064", "desc": "2858"}, {"messageId": "2849", "data": "3065", "fix": "3066", "desc": "3067"}, {"messageId": "2849", "data": "3068", "fix": "3069", "desc": "3070"}, {"messageId": "2849", "data": "3071", "fix": "3072", "desc": "3034"}, {"messageId": "2849", "data": "3073", "fix": "3074", "desc": "2852"}, {"messageId": "2849", "data": "3075", "fix": "3076", "desc": "2858"}, {"messageId": "2849", "data": "3077", "fix": "3078", "desc": "2852"}, {"messageId": "2849", "data": "3079", "fix": "3080", "desc": "3081"}, {"messageId": "2849", "data": "3082", "fix": "3083", "desc": "2858"}, {"messageId": "2849", "data": "3084", "fix": "3085", "desc": "2861"}, {"fix": "3086", "messageId": "2865", "data": "3087", "desc": "2894"}, {"fix": "3088", "messageId": "2865", "data": "3089", "desc": "2894"}, {"fix": "3090", "messageId": "2865", "data": "3091", "desc": "2894"}, {"messageId": "2849", "data": "3092", "fix": "3093", "desc": "2852"}, {"messageId": "2849", "data": "3094", "fix": "3095", "desc": "2855"}, {"messageId": "2849", "data": "3096", "fix": "3097", "desc": "2858"}, {"messageId": "3035", "data": "3098", "fix": "3099", "desc": "3038"}, {"messageId": "3035", "data": "3100", "fix": "3101", "desc": "3041"}, {"messageId": "3035", "data": "3102", "fix": "3103", "desc": "3044"}, {"messageId": "3035", "data": "3104", "fix": "3105", "desc": "3047"}, {"messageId": "3035", "data": "3106", "fix": "3107", "desc": "3038"}, {"messageId": "3035", "data": "3108", "fix": "3109", "desc": "3041"}, {"messageId": "3035", "data": "3110", "fix": "3111", "desc": "3044"}, {"messageId": "3035", "data": "3112", "fix": "3113", "desc": "3047"}, {"messageId": "2849", "data": "3114", "fix": "3115", "desc": "2861"}, {"fix": "3116", "messageId": "2865", "data": "3117", "desc": "2867"}, {"messageId": "3035", "data": "3118", "fix": "3119", "desc": "3038"}, {"messageId": "3035", "data": "3120", "fix": "3121", "desc": "3041"}, {"messageId": "3035", "data": "3122", "fix": "3123", "desc": "3044"}, {"messageId": "3035", "data": "3124", "fix": "3125", "desc": "3047"}, {"messageId": "3035", "data": "3126", "fix": "3127", "desc": "3038"}, {"messageId": "3035", "data": "3128", "fix": "3129", "desc": "3041"}, {"messageId": "3035", "data": "3130", "fix": "3131", "desc": "3044"}, {"messageId": "3035", "data": "3132", "fix": "3133", "desc": "3047"}, {"messageId": "3035", "data": "3134", "fix": "3135", "desc": "3038"}, {"messageId": "3035", "data": "3136", "fix": "3137", "desc": "3041"}, {"messageId": "3035", "data": "3138", "fix": "3139", "desc": "3044"}, {"messageId": "3035", "data": "3140", "fix": "3141", "desc": "3047"}, {"messageId": "3035", "data": "3142", "fix": "3143", "desc": "3038"}, {"messageId": "3035", "data": "3144", "fix": "3145", "desc": "3041"}, {"messageId": "3035", "data": "3146", "fix": "3147", "desc": "3044"}, {"messageId": "3035", "data": "3148", "fix": "3149", "desc": "3047"}, {"messageId": "2849", "data": "3150", "fix": "3151", "desc": "3152"}, {"fix": "3153", "messageId": "2865", "data": "3154", "desc": "2867"}, {"messageId": "3035", "data": "3155", "fix": "3156", "desc": "3038"}, {"messageId": "3035", "data": "3157", "fix": "3158", "desc": "3041"}, {"messageId": "3035", "data": "3159", "fix": "3160", "desc": "3044"}, {"messageId": "3035", "data": "3161", "fix": "3162", "desc": "3047"}, {"messageId": "3035", "data": "3163", "fix": "3164", "desc": "3038"}, {"messageId": "3035", "data": "3165", "fix": "3166", "desc": "3041"}, {"messageId": "3035", "data": "3167", "fix": "3168", "desc": "3044"}, {"messageId": "3035", "data": "3169", "fix": "3170", "desc": "3047"}, {"messageId": "3035", "data": "3171", "fix": "3172", "desc": "3038"}, {"messageId": "3035", "data": "3173", "fix": "3174", "desc": "3041"}, {"messageId": "3035", "data": "3175", "fix": "3176", "desc": "3044"}, {"messageId": "3035", "data": "3177", "fix": "3178", "desc": "3047"}, {"messageId": "3035", "data": "3179", "fix": "3180", "desc": "3038"}, {"messageId": "3035", "data": "3181", "fix": "3182", "desc": "3041"}, {"messageId": "3035", "data": "3183", "fix": "3184", "desc": "3044"}, {"messageId": "3035", "data": "3185", "fix": "3186", "desc": "3047"}, {"messageId": "3035", "data": "3187", "fix": "3188", "desc": "3038"}, {"messageId": "3035", "data": "3189", "fix": "3190", "desc": "3041"}, {"messageId": "3035", "data": "3191", "fix": "3192", "desc": "3044"}, {"messageId": "3035", "data": "3193", "fix": "3194", "desc": "3047"}, {"messageId": "3035", "data": "3195", "fix": "3196", "desc": "3038"}, {"messageId": "3035", "data": "3197", "fix": "3198", "desc": "3041"}, {"messageId": "3035", "data": "3199", "fix": "3200", "desc": "3044"}, {"messageId": "3035", "data": "3201", "fix": "3202", "desc": "3047"}, {"messageId": "2849", "data": "3203", "fix": "3204", "desc": "3060"}, {"messageId": "2849", "data": "3205", "fix": "3206", "desc": "3207"}, {"messageId": "2849", "data": "3208", "fix": "3209", "desc": "3210"}, {"messageId": "2849", "data": "3211", "fix": "3212", "desc": "3213"}, {"messageId": "2849", "data": "3214", "fix": "3215", "desc": "3216"}, {"messageId": "2849", "data": "3217", "fix": "3218", "desc": "3219"}, {"messageId": "2849", "data": "3220", "fix": "3221", "desc": "2858"}, {"messageId": "3035", "data": "3222", "fix": "3223", "desc": "3038"}, {"messageId": "3035", "data": "3224", "fix": "3225", "desc": "3041"}, {"messageId": "3035", "data": "3226", "fix": "3227", "desc": "3044"}, {"messageId": "3035", "data": "3228", "fix": "3229", "desc": "3047"}, {"messageId": "3035", "data": "3230", "fix": "3231", "desc": "3038"}, {"messageId": "3035", "data": "3232", "fix": "3233", "desc": "3041"}, {"messageId": "3035", "data": "3234", "fix": "3235", "desc": "3044"}, {"messageId": "3035", "data": "3236", "fix": "3237", "desc": "3047"}, {"messageId": "2849", "data": "3238", "fix": "3239", "desc": "2852"}, {"messageId": "2849", "data": "3240", "fix": "3241", "desc": "2855"}, {"messageId": "2849", "data": "3242", "fix": "3243", "desc": "2858"}, {"messageId": "2849", "data": "3244", "fix": "3245", "desc": "3067"}, {"messageId": "2849", "data": "3246", "fix": "3247", "desc": "3248"}, {"messageId": "2849", "data": "3249", "fix": "3250", "desc": "2852"}, {"messageId": "2849", "data": "3251", "fix": "3252", "desc": "3081"}, {"messageId": "2849", "data": "3253", "fix": "3254", "desc": "2858"}, {"messageId": "2849", "data": "3255", "fix": "3256", "desc": "3257"}, {"messageId": "2849", "data": "3258", "fix": "3259", "desc": "3260"}, {"messageId": "3035", "data": "3261", "fix": "3262", "desc": "3038"}, {"messageId": "3035", "data": "3263", "fix": "3264", "desc": "3041"}, {"messageId": "3035", "data": "3265", "fix": "3266", "desc": "3044"}, {"messageId": "3035", "data": "3267", "fix": "3268", "desc": "3047"}, {"messageId": "3035", "data": "3269", "fix": "3270", "desc": "3038"}, {"messageId": "3035", "data": "3271", "fix": "3272", "desc": "3041"}, {"messageId": "3035", "data": "3273", "fix": "3274", "desc": "3044"}, {"messageId": "3035", "data": "3275", "fix": "3276", "desc": "3047"}, {"messageId": "3035", "data": "3277", "fix": "3278", "desc": "3038"}, {"messageId": "3035", "data": "3279", "fix": "3280", "desc": "3041"}, {"messageId": "3035", "data": "3281", "fix": "3282", "desc": "3044"}, {"messageId": "3035", "data": "3283", "fix": "3284", "desc": "3047"}, {"messageId": "3035", "data": "3285", "fix": "3286", "desc": "3038"}, {"messageId": "3035", "data": "3287", "fix": "3288", "desc": "3041"}, {"messageId": "3035", "data": "3289", "fix": "3290", "desc": "3044"}, {"messageId": "3035", "data": "3291", "fix": "3292", "desc": "3047"}, {"messageId": "2849", "data": "3293", "fix": "3294", "desc": "2852"}, {"messageId": "2849", "data": "3295", "fix": "3296", "desc": "2858"}, {"messageId": "3035", "data": "3297", "fix": "3298", "desc": "3038"}, {"messageId": "3035", "data": "3299", "fix": "3300", "desc": "3041"}, {"messageId": "3035", "data": "3301", "fix": "3302", "desc": "3044"}, {"messageId": "3035", "data": "3303", "fix": "3304", "desc": "3047"}, {"messageId": "3035", "data": "3305", "fix": "3306", "desc": "3038"}, {"messageId": "3035", "data": "3307", "fix": "3308", "desc": "3041"}, {"messageId": "3035", "data": "3309", "fix": "3310", "desc": "3044"}, {"messageId": "3035", "data": "3311", "fix": "3312", "desc": "3047"}, {"messageId": "2849", "data": "3313", "fix": "3314", "desc": "2852"}, {"messageId": "2849", "data": "3315", "fix": "3316", "desc": "3081"}, {"messageId": "2849", "data": "3317", "fix": "3318", "desc": "2858"}, {"messageId": "2849", "data": "3319", "fix": "3320", "desc": "2861"}, {"messageId": "2849", "data": "3321", "fix": "3322", "desc": "3323"}, {"messageId": "2849", "data": "3324", "fix": "3325", "desc": "3034"}, {"messageId": "2849", "data": "3326", "fix": "3327", "desc": "2861"}, {"messageId": "2849", "data": "3328", "fix": "3329", "desc": "3330"}, {"messageId": "2849", "data": "3331", "fix": "3332", "desc": "3333"}, {"messageId": "2849", "data": "3334", "fix": "3335", "desc": "3336"}, {"desc": "3337", "fix": "3338"}, {"desc": "3339", "fix": "3340"}, {"messageId": "2849", "data": "3341", "fix": "3342", "desc": "3343"}, {"desc": "3344", "fix": "3345"}, {"desc": "3346", "fix": "3347"}, {"messageId": "2849", "data": "3348", "fix": "3349", "desc": "3350"}, {"messageId": "2849", "data": "3351", "fix": "3352", "desc": "3353"}, {"messageId": "2849", "data": "3354", "fix": "3355", "desc": "3356"}, {"messageId": "2849", "data": "3357", "fix": "3358", "desc": "3356"}, {"desc": "3359", "fix": "3360"}, {"fix": "3361", "messageId": "2865", "data": "3362", "desc": "2880"}, {"fix": "3363", "messageId": "2865", "data": "3364", "desc": "2867"}, {"fix": "3365", "messageId": "2865", "data": "3366", "desc": "2880"}, {"fix": "3367", "messageId": "2865", "data": "3368", "desc": "2867"}, {"fix": "3369", "messageId": "2865", "data": "3370", "desc": "2880"}, {"fix": "3371", "messageId": "2865", "data": "3372", "desc": "2867"}, {"fix": "3373", "messageId": "2865", "data": "3374", "desc": "2880"}, {"fix": "3375", "messageId": "2865", "data": "3376", "desc": "2867"}, {"fix": "3377", "messageId": "2865", "data": "3378", "desc": "2880"}, {"fix": "3379", "messageId": "2865", "data": "3380", "desc": "2867"}, {"fix": "3381", "messageId": "2865", "data": "3382", "desc": "2867"}, {"messageId": "2849", "data": "3383", "fix": "3384", "desc": "3385"}, {"fix": "3386", "messageId": "2865", "data": "3387", "desc": "2894"}, {"fix": "3388", "messageId": "2865", "data": "3389", "desc": "2867"}, {"messageId": "2849", "data": "3390", "fix": "3391", "desc": "3392"}, {"desc": "3393", "fix": "3394"}, {"messageId": "2849", "data": "3395", "fix": "3396", "desc": "3397"}, {"desc": "3398", "fix": "3399"}, {"desc": "3400", "fix": "3401"}, {"desc": "3402", "fix": "3403"}, {"desc": "3404", "fix": "3405"}, {"fix": "3406", "messageId": "2865", "data": "3407", "desc": "2880"}, {"fix": "3408", "messageId": "2865", "data": "3409", "desc": "2867"}, {"fix": "3410", "messageId": "2865", "data": "3411", "desc": "2867"}, {"messageId": "2849", "data": "3412", "fix": "3413", "desc": "3414"}, {"messageId": "2849", "data": "3415", "fix": "3416", "desc": "3417"}, {"messageId": "2849", "data": "3418", "fix": "3419", "desc": "2852"}, {"messageId": "2849", "data": "3420", "fix": "3421", "desc": "2855"}, {"messageId": "2849", "data": "3422", "fix": "3423", "desc": "2858"}, {"messageId": "2849", "data": "3424", "fix": "3425", "desc": "2861"}, {"fix": "3426", "messageId": "2865", "data": "3427", "desc": "2894"}, {"messageId": "2849", "data": "3428", "fix": "3429", "desc": "3430"}, {"messageId": "2849", "data": "3431", "fix": "3432", "desc": "2861"}, {"messageId": "2849", "data": "3433", "fix": "3434", "desc": "3435"}, {"messageId": "2849", "data": "3436", "fix": "3437", "desc": "2861"}, {"messageId": "2849", "data": "3438", "fix": "3439", "desc": "3440"}, {"fix": "3441", "messageId": "2865", "data": "3442", "desc": "2867"}, {"messageId": "2849", "data": "3443", "fix": "3444", "desc": "3440"}, {"messageId": "2849", "data": "3445", "fix": "3446", "desc": "3440"}, {"messageId": "2849", "data": "3447", "fix": "3448", "desc": "3449"}, {"messageId": "2849", "data": "3450", "fix": "3451", "desc": "2861"}, {"fix": "3452", "messageId": "2865", "data": "3453", "desc": "2867"}, {"messageId": "2849", "data": "3454", "fix": "3455", "desc": "2861"}, {"messageId": "2849", "data": "3456", "fix": "3457", "desc": "3034"}, {"messageId": "2849", "data": "3458", "fix": "3459", "desc": "2852"}, {"messageId": "2849", "data": "3460", "fix": "3461", "desc": "2855"}, {"messageId": "2849", "data": "3462", "fix": "3463", "desc": "2858"}, {"messageId": "2849", "data": "3464", "fix": "3465", "desc": "2861"}, {"messageId": "2849", "data": "3466", "fix": "3467", "desc": "3034"}, {"messageId": "3035", "data": "3468", "fix": "3469", "desc": "3038"}, {"messageId": "3035", "data": "3470", "fix": "3471", "desc": "3041"}, {"messageId": "3035", "data": "3472", "fix": "3473", "desc": "3044"}, {"messageId": "3035", "data": "3474", "fix": "3475", "desc": "3047"}, {"messageId": "3035", "data": "3476", "fix": "3477", "desc": "3038"}, {"messageId": "3035", "data": "3478", "fix": "3479", "desc": "3041"}, {"messageId": "3035", "data": "3480", "fix": "3481", "desc": "3044"}, {"messageId": "3035", "data": "3482", "fix": "3483", "desc": "3047"}, {"messageId": "3035", "data": "3484", "fix": "3485", "desc": "3038"}, {"messageId": "3035", "data": "3486", "fix": "3487", "desc": "3041"}, {"messageId": "3035", "data": "3488", "fix": "3489", "desc": "3044"}, {"messageId": "3035", "data": "3490", "fix": "3491", "desc": "3047"}, {"messageId": "3035", "data": "3492", "fix": "3493", "desc": "3038"}, {"messageId": "3035", "data": "3494", "fix": "3495", "desc": "3041"}, {"messageId": "3035", "data": "3496", "fix": "3497", "desc": "3044"}, {"messageId": "3035", "data": "3498", "fix": "3499", "desc": "3047"}, {"messageId": "2849", "data": "3500", "fix": "3501", "desc": "3502"}, {"messageId": "2849", "data": "3503", "fix": "3504", "desc": "2852"}, {"messageId": "2849", "data": "3505", "fix": "3506", "desc": "2855"}, {"messageId": "2849", "data": "3507", "fix": "3508", "desc": "2858"}, {"messageId": "2849", "data": "3509", "fix": "3510", "desc": "2861"}, {"messageId": "2849", "data": "3511", "fix": "3512", "desc": "3513"}, {"messageId": "2849", "data": "3514", "fix": "3515", "desc": "3516"}, {"messageId": "2849", "data": "3517", "fix": "3518", "desc": "3513"}, {"messageId": "2849", "data": "3519", "fix": "3520", "desc": "3516"}, {"messageId": "2849", "data": "3521", "fix": "3522", "desc": "3523"}, {"fix": "3524", "messageId": "2865", "data": "3525", "desc": "2894"}, {"messageId": "2849", "data": "3526", "fix": "3527", "desc": "3528"}, {"fix": "3529", "messageId": "2865", "data": "3530", "desc": "2894"}, {"messageId": "3035", "data": "3531", "fix": "3532", "desc": "3038"}, {"messageId": "3035", "data": "3533", "fix": "3534", "desc": "3041"}, {"messageId": "3035", "data": "3535", "fix": "3536", "desc": "3044"}, {"messageId": "3035", "data": "3537", "fix": "3538", "desc": "3047"}, {"messageId": "3035", "data": "3539", "fix": "3540", "desc": "3038"}, {"messageId": "3035", "data": "3541", "fix": "3542", "desc": "3041"}, {"messageId": "3035", "data": "3543", "fix": "3544", "desc": "3044"}, {"messageId": "3035", "data": "3545", "fix": "3546", "desc": "3047"}, {"messageId": "2849", "data": "3547", "fix": "3548", "desc": "3549"}, {"messageId": "2849", "data": "3550", "fix": "3551", "desc": "3552"}, {"messageId": "2849", "data": "3553", "fix": "3554", "desc": "3555"}, {"fix": "3556", "messageId": "2865", "data": "3557", "desc": "2894"}, {"messageId": "2849", "data": "3558", "fix": "3559", "desc": "3560"}, {"messageId": "2849", "data": "3561", "fix": "3562", "desc": "3523"}, {"messageId": "2849", "data": "3563", "fix": "3564", "desc": "3528"}, {"messageId": "3035", "data": "3565", "fix": "3566", "desc": "3038"}, {"messageId": "3035", "data": "3567", "fix": "3568", "desc": "3041"}, {"messageId": "3035", "data": "3569", "fix": "3570", "desc": "3044"}, {"messageId": "3035", "data": "3571", "fix": "3572", "desc": "3047"}, {"messageId": "3035", "data": "3573", "fix": "3574", "desc": "3038"}, {"messageId": "3035", "data": "3575", "fix": "3576", "desc": "3041"}, {"messageId": "3035", "data": "3577", "fix": "3578", "desc": "3044"}, {"messageId": "3035", "data": "3579", "fix": "3580", "desc": "3047"}, {"messageId": "2849", "data": "3581", "fix": "3582", "desc": "3583"}, {"messageId": "2849", "data": "3584", "fix": "3585", "desc": "3586"}, {"messageId": "2849", "data": "3587", "fix": "3588", "desc": "3589"}, {"messageId": "2849", "data": "3590", "fix": "3591", "desc": "3417"}, {"messageId": "2849", "data": "3592", "fix": "3593", "desc": "2861"}, {"messageId": "2849", "data": "3594", "fix": "3595", "desc": "3596"}, {"messageId": "2849", "data": "3597", "fix": "3598", "desc": "3599"}, {"messageId": "2849", "data": "3600", "fix": "3601", "desc": "3602"}, {"messageId": "2849", "data": "3603", "fix": "3604", "desc": "3605"}, {"messageId": "2849", "data": "3606", "fix": "3607", "desc": "3608"}, {"messageId": "2849", "data": "3609", "fix": "3610", "desc": "3611"}, {"messageId": "2849", "data": "3612", "fix": "3613", "desc": "3528"}, {"messageId": "2849", "data": "3614", "fix": "3615", "desc": "2861"}, {"messageId": "2849", "data": "3616", "fix": "3617", "desc": "3618"}, {"messageId": "2849", "data": "3619", "fix": "3620", "desc": "3621"}, {"messageId": "2849", "data": "3622", "fix": "3623", "desc": "3624"}, {"desc": "3625", "fix": "3626"}, {"fix": "3627", "messageId": "2865", "data": "3628", "desc": "2867"}, {"desc": "3629", "fix": "3630"}, {"messageId": "2849", "data": "3631", "fix": "3632", "desc": "3633"}, {"fix": "3634", "messageId": "2865", "data": "3635", "desc": "2867"}, {"fix": "3636", "messageId": "2865", "data": "3637", "desc": "2894"}, {"fix": "3638", "messageId": "2865", "data": "3639", "desc": "2894"}, {"fix": "3640", "messageId": "2865", "data": "3641", "desc": "2894"}, {"messageId": "2849", "data": "3642", "fix": "3643", "desc": "3644"}, {"messageId": "2849", "data": "3645", "fix": "3646", "desc": "3647"}, {"fix": "3648", "messageId": "2865", "data": "3649", "desc": "2880"}, {"fix": "3650", "messageId": "2865", "data": "3651", "desc": "2867"}, {"fix": "3652", "messageId": "2865", "data": "3653", "desc": "2880"}, {"fix": "3654", "messageId": "2865", "data": "3655", "desc": "2880"}, {"fix": "3656", "messageId": "2865", "data": "3657", "desc": "2867"}, {"fix": "3658", "messageId": "2865", "data": "3659", "desc": "2867"}, {"messageId": "2849", "data": "3660", "fix": "3661", "desc": "3662"}, {"messageId": "2849", "data": "3663", "fix": "3664", "desc": "3665"}, {"messageId": "2849", "data": "3666", "fix": "3667", "desc": "3668"}, {"fix": "3669", "messageId": "2865", "data": "3670", "desc": "2880"}, {"fix": "3671", "messageId": "2865", "data": "3672", "desc": "2880"}, {"fix": "3673", "messageId": "2865", "data": "3674", "desc": "2880"}, {"fix": "3675", "messageId": "2865", "data": "3676", "desc": "2880"}, {"fix": "3677", "messageId": "2865", "data": "3678", "desc": "2880"}, {"fix": "3679", "messageId": "2865", "data": "3680", "desc": "2867"}, {"fix": "3681", "messageId": "2865", "data": "3682", "desc": "2867"}, {"fix": "3683", "messageId": "2865", "data": "3684", "desc": "2867"}, {"fix": "3685", "messageId": "2865", "data": "3686", "desc": "2867"}, {"fix": "3687", "messageId": "2865", "data": "3688", "desc": "2867"}, {"fix": "3689", "messageId": "2865", "data": "3690", "desc": "2880"}, {"fix": "3691", "messageId": "2865", "data": "3692", "desc": "2880"}, {"fix": "3693", "messageId": "2865", "data": "3694", "desc": "2867"}, {"fix": "3695", "messageId": "2865", "data": "3696", "desc": "2880"}, {"fix": "3697", "messageId": "2865", "data": "3698", "desc": "2867"}, {"fix": "3699", "messageId": "2865", "data": "3700", "desc": "2880"}, {"fix": "3701", "messageId": "2865", "data": "3702", "desc": "2880"}, {"fix": "3703", "messageId": "2865", "data": "3704", "desc": "2880"}, {"fix": "3705", "messageId": "2865", "data": "3706", "desc": "2867"}, {"fix": "3707", "messageId": "2865", "data": "3708", "desc": "2880"}, {"fix": "3709", "messageId": "2865", "data": "3710", "desc": "2867"}, {"messageId": "3035", "data": "3711", "fix": "3712", "desc": "3038"}, {"messageId": "3035", "data": "3713", "fix": "3714", "desc": "3041"}, {"messageId": "3035", "data": "3715", "fix": "3716", "desc": "3044"}, {"messageId": "3035", "data": "3717", "fix": "3718", "desc": "3047"}, {"messageId": "3035", "data": "3719", "fix": "3720", "desc": "3038"}, {"messageId": "3035", "data": "3721", "fix": "3722", "desc": "3041"}, {"messageId": "3035", "data": "3723", "fix": "3724", "desc": "3044"}, {"messageId": "3035", "data": "3725", "fix": "3726", "desc": "3047"}, {"messageId": "2849", "data": "3727", "fix": "3728", "desc": "3555"}, {"messageId": "2849", "data": "3729", "fix": "3730", "desc": "3152"}, {"messageId": "2849", "data": "3731", "fix": "3732", "desc": "3733"}, {"messageId": "2849", "data": "3734", "fix": "3735", "desc": "3736"}, {"messageId": "2849", "data": "3737", "fix": "3738", "desc": "3739"}, {"messageId": "2849", "data": "3740", "fix": "3741", "desc": "3742"}, {"messageId": "2849", "data": "3743", "fix": "3744", "desc": "3745"}, {"messageId": "2849", "data": "3746", "fix": "3747", "desc": "3748"}, {"messageId": "2849", "data": "3749", "fix": "3750", "desc": "3751"}, {"messageId": "2849", "data": "3752", "fix": "3753", "desc": "3754"}, {"desc": "3755", "fix": "3756"}, {"messageId": "2849", "data": "3757", "fix": "3758", "desc": "3759"}, {"desc": "3760", "fix": "3761"}, {"messageId": "2849", "data": "3762", "fix": "3763", "desc": "3764"}, {"messageId": "2849", "data": "3765", "fix": "3766", "desc": "3528"}, {"messageId": "3035", "data": "3767", "fix": "3768", "desc": "3038"}, {"messageId": "3035", "data": "3769", "fix": "3770", "desc": "3041"}, {"messageId": "3035", "data": "3771", "fix": "3772", "desc": "3044"}, {"messageId": "3035", "data": "3773", "fix": "3774", "desc": "3047"}, {"messageId": "3035", "data": "3775", "fix": "3776", "desc": "3038"}, {"messageId": "3035", "data": "3777", "fix": "3778", "desc": "3041"}, {"messageId": "3035", "data": "3779", "fix": "3780", "desc": "3044"}, {"messageId": "3035", "data": "3781", "fix": "3782", "desc": "3047"}, {"messageId": "3035", "data": "3783", "fix": "3784", "desc": "3038"}, {"messageId": "3035", "data": "3785", "fix": "3786", "desc": "3041"}, {"messageId": "3035", "data": "3787", "fix": "3788", "desc": "3044"}, {"messageId": "3035", "data": "3789", "fix": "3790", "desc": "3047"}, {"messageId": "3035", "data": "3791", "fix": "3792", "desc": "3038"}, {"messageId": "3035", "data": "3793", "fix": "3794", "desc": "3041"}, {"messageId": "3035", "data": "3795", "fix": "3796", "desc": "3044"}, {"messageId": "3035", "data": "3797", "fix": "3798", "desc": "3047"}, {"messageId": "2849", "data": "3799", "fix": "3800", "desc": "2861"}, {"messageId": "2849", "data": "3801", "fix": "3802", "desc": "3803"}, {"desc": "3804", "fix": "3805"}, {"desc": "3806", "fix": "3807"}, {"fix": "3808", "messageId": "2865", "data": "3809", "desc": "2867"}, {"messageId": "2849", "data": "3810", "fix": "3811", "desc": "3812"}, {"messageId": "2849", "data": "3813", "fix": "3814", "desc": "3815"}, {"messageId": "2849", "data": "3816", "fix": "3817", "desc": "3818"}, {"messageId": "2849", "data": "3819", "fix": "3820", "desc": "2852"}, {"messageId": "2849", "data": "3821", "fix": "3822", "desc": "3081"}, {"messageId": "2849", "data": "3823", "fix": "3824", "desc": "2858"}, {"messageId": "3035", "data": "3825", "fix": "3826", "desc": "3038"}, {"messageId": "3035", "data": "3827", "fix": "3828", "desc": "3041"}, {"messageId": "3035", "data": "3829", "fix": "3830", "desc": "3044"}, {"messageId": "3035", "data": "3831", "fix": "3832", "desc": "3047"}, {"messageId": "3035", "data": "3833", "fix": "3834", "desc": "3038"}, {"messageId": "3035", "data": "3835", "fix": "3836", "desc": "3041"}, {"messageId": "3035", "data": "3837", "fix": "3838", "desc": "3044"}, {"messageId": "3035", "data": "3839", "fix": "3840", "desc": "3047"}, {"messageId": "3035", "data": "3841", "fix": "3842", "desc": "3038"}, {"messageId": "3035", "data": "3843", "fix": "3844", "desc": "3041"}, {"messageId": "3035", "data": "3845", "fix": "3846", "desc": "3044"}, {"messageId": "3035", "data": "3847", "fix": "3848", "desc": "3047"}, {"messageId": "3035", "data": "3849", "fix": "3850", "desc": "3038"}, {"messageId": "3035", "data": "3851", "fix": "3852", "desc": "3041"}, {"messageId": "3035", "data": "3853", "fix": "3854", "desc": "3044"}, {"messageId": "3035", "data": "3855", "fix": "3856", "desc": "3047"}, {"messageId": "3035", "data": "3857", "fix": "3858", "desc": "3038"}, {"messageId": "3035", "data": "3859", "fix": "3860", "desc": "3041"}, {"messageId": "3035", "data": "3861", "fix": "3862", "desc": "3044"}, {"messageId": "3035", "data": "3863", "fix": "3864", "desc": "3047"}, {"messageId": "2849", "data": "3865", "fix": "3866", "desc": "3867"}, {"messageId": "2849", "data": "3868", "fix": "3869", "desc": "3440"}, {"fix": "3870", "messageId": "2865", "data": "3871", "desc": "2867"}, {"fix": "3872", "messageId": "2865", "data": "3873", "desc": "2894"}, {"messageId": "2849", "data": "3874", "fix": "3875", "desc": "3876"}, {"messageId": "2849", "data": "3877", "fix": "3878", "desc": "3879"}, {"messageId": "2849", "data": "3880", "fix": "3881", "desc": "3882"}, {"messageId": "2849", "data": "3883", "fix": "3884", "desc": "3885"}, {"messageId": "2849", "data": "3886", "fix": "3887", "desc": "3624"}, {"messageId": "2849", "data": "3888", "fix": "3889", "desc": "3890"}, {"messageId": "2849", "data": "3891", "fix": "3892", "desc": "3893"}, {"messageId": "2849", "data": "3894", "fix": "3895", "desc": "3896"}, {"fix": "3897", "messageId": "2865", "data": "3898", "desc": "2894"}, {"messageId": "3035", "data": "3899", "fix": "3900", "desc": "3038"}, {"messageId": "3035", "data": "3901", "fix": "3902", "desc": "3041"}, {"messageId": "3035", "data": "3903", "fix": "3904", "desc": "3044"}, {"messageId": "3035", "data": "3905", "fix": "3906", "desc": "3047"}, {"messageId": "3035", "data": "3907", "fix": "3908", "desc": "3038"}, {"messageId": "3035", "data": "3909", "fix": "3910", "desc": "3041"}, {"messageId": "3035", "data": "3911", "fix": "3912", "desc": "3044"}, {"messageId": "3035", "data": "3913", "fix": "3914", "desc": "3047"}, {"messageId": "2849", "data": "3915", "fix": "3916", "desc": "3917"}, {"fix": "3918", "messageId": "2865", "data": "3919", "desc": "2894"}, {"messageId": "3035", "data": "3920", "fix": "3921", "desc": "3038"}, {"messageId": "3035", "data": "3922", "fix": "3923", "desc": "3041"}, {"messageId": "3035", "data": "3924", "fix": "3925", "desc": "3044"}, {"messageId": "3035", "data": "3926", "fix": "3927", "desc": "3047"}, {"messageId": "2849", "data": "3928", "fix": "3929", "desc": "3930"}, {"messageId": "2849", "data": "3931", "fix": "3932", "desc": "3933"}, {"messageId": "2849", "data": "3934", "fix": "3935", "desc": "3936"}, {"messageId": "2849", "data": "3937", "fix": "3938", "desc": "3939"}, {"messageId": "2849", "data": "3940", "fix": "3941", "desc": "3942"}, {"messageId": "2849", "data": "3943", "fix": "3944", "desc": "3945"}, {"messageId": "2849", "data": "3946", "fix": "3947", "desc": "3948"}, {"messageId": "2849", "data": "3949", "fix": "3950", "desc": "3951"}, {"messageId": "2849", "data": "3952", "fix": "3953", "desc": "3954"}, {"messageId": "2849", "data": "3955", "fix": "3956", "desc": "3957"}, {"messageId": "2849", "data": "3958", "fix": "3959", "desc": "3960"}, {"messageId": "2849", "data": "3961", "fix": "3962", "desc": "3930"}, {"messageId": "2849", "data": "3963", "fix": "3964", "desc": "3933"}, {"messageId": "2849", "data": "3965", "fix": "3966", "desc": "3936"}, {"messageId": "2849", "data": "3967", "fix": "3968", "desc": "3939"}, {"messageId": "2849", "data": "3969", "fix": "3970", "desc": "3945"}, {"messageId": "2849", "data": "3971", "fix": "3972", "desc": "3948"}, {"messageId": "2849", "data": "3973", "fix": "3974", "desc": "3951"}, {"messageId": "2849", "data": "3975", "fix": "3976", "desc": "3957"}, {"messageId": "2849", "data": "3977", "fix": "3978", "desc": "3960"}, {"messageId": "2849", "data": "3979", "fix": "3980", "desc": "3981"}, {"fix": "3982", "messageId": "2865", "data": "3983", "desc": "2867"}, {"fix": "3984", "messageId": "2865", "data": "3985", "desc": "2867"}, {"fix": "3986", "messageId": "2865", "data": "3987", "desc": "2867"}, {"fix": "3988", "messageId": "2865", "data": "3989", "desc": "2894"}, {"fix": "3990", "messageId": "2865", "data": "3991", "desc": "2867"}, {"fix": "3992", "messageId": "2865", "data": "3993", "desc": "2894"}, {"fix": "3994", "messageId": "2865", "data": "3995", "desc": "2880"}, {"messageId": "2849", "data": "3996", "fix": "3997", "desc": "3998"}, {"messageId": "2849", "data": "3999", "fix": "4000", "desc": "3867"}, {"fix": "4001", "messageId": "2865", "data": "4002", "desc": "2880"}, {"fix": "4003", "messageId": "2865", "data": "4004", "desc": "2880"}, {"fix": "4005", "messageId": "2865", "data": "4006", "desc": "2880"}, {"fix": "4007", "messageId": "2865", "data": "4008", "desc": "2880"}, {"fix": "4009", "messageId": "2865", "data": "4010", "desc": "2867"}, {"fix": "4011", "messageId": "2865", "data": "4012", "desc": "2880"}, {"fix": "4013", "messageId": "2865", "data": "4014", "desc": "2867"}, {"fix": "4015", "messageId": "2865", "data": "4016", "desc": "2880"}, {"fix": "4017", "messageId": "2865", "data": "4018", "desc": "2867"}, {"fix": "4019", "messageId": "2865", "data": "4020", "desc": "2880"}, {"fix": "4021", "messageId": "2865", "data": "4022", "desc": "2867"}, {"fix": "4023", "messageId": "2865", "data": "4024", "desc": "2867"}, {"fix": "4025", "messageId": "2865", "data": "4026", "desc": "2880"}, {"fix": "4027", "messageId": "2865", "data": "4028", "desc": "2867"}, {"fix": "4029", "messageId": "2865", "data": "4030", "desc": "2894"}, {"fix": "4031", "messageId": "2865", "data": "4032", "desc": "2880"}, {"fix": "4033", "messageId": "2865", "data": "4034", "desc": "2867"}, {"fix": "4035", "messageId": "2865", "data": "4036", "desc": "2867"}, {"fix": "4037", "messageId": "2865", "data": "4038", "desc": "2867"}, "removeVar", {"varName": "4039"}, {"range": "4040", "text": "4041"}, "Remove unused variable 'Hero<PERSON>itle'.", {"varName": "4042"}, {"range": "4043", "text": "4041"}, "Remove unused variable 'SectionTitle'.", {"varName": "4044"}, {"range": "4045", "text": "4041"}, "Remove unused variable 'BodyText'.", {"varName": "4046"}, {"range": "4047", "text": "4041"}, "Remove unused variable 'UnifiedButton'.", "Update the dependencies array to be: [router, verifyTokenAndLoadData]", {"range": "4048", "text": "4049"}, {"range": "4050", "text": "4041"}, "removeConsole", {"propertyName": "4051"}, "Remove the console.error().", {"range": "4052", "text": "4041"}, {"propertyName": "4051"}, {"range": "4053", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4046"}, {"range": "4054", "text": "4041"}, {"range": "4055", "text": "4041"}, {"propertyName": "4051"}, {"range": "4056", "text": "4041"}, {"propertyName": "4051"}, {"range": "4057", "text": "4041"}, {"propertyName": "4058"}, "Remove the console.log().", {"range": "4059", "text": "4041"}, {"propertyName": "4051"}, {"range": "4060", "text": "4041"}, {"propertyName": "4051"}, {"range": "4061", "text": "4041"}, {"propertyName": "4051"}, {"range": "4062", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4063"}, {"range": "4064", "text": "4041"}, "Remove unused variable 'crypto'.", {"range": "4065", "text": "4041"}, {"propertyName": "4066"}, "Remove the console.warn().", {"range": "4067", "text": "4041"}, {"propertyName": "4066"}, {"range": "4068", "text": "4041"}, {"propertyName": "4058"}, {"range": "4069", "text": "4041"}, {"propertyName": "4066"}, {"range": "4070", "text": "4041"}, {"propertyName": "4066"}, {"range": "4071", "text": "4041"}, {"propertyName": "4066"}, {"range": "4072", "text": "4041"}, {"propertyName": "4066"}, {"range": "4073", "text": "4041"}, {"propertyName": "4066"}, {"range": "4074", "text": "4041"}, {"propertyName": "4051"}, {"range": "4075", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4076"}, {"range": "4077", "text": "4041"}, "Remove unused variable 'hashPassword'.", {"range": "4078", "text": "4041"}, {"propertyName": "4066"}, {"range": "4079", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4080"}, {"range": "4081", "text": "4041"}, "Remove unused variable 'validateSchema'.", {"varName": "4082"}, {"range": "4083", "text": "4041"}, "Remove unused variable 'ValidationError'.", {"varName": "4084"}, {"range": "4085", "text": "4041"}, "Remove unused variable 'handleApiError'.", {"range": "4086", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4087"}, {"range": "4088", "text": "4041"}, "Remove unused variable 'emailSent'.", {"range": "4089", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4090"}, {"range": "4091", "text": "4041"}, "Remove unused variable 'emailContent'.", {"range": "4092", "text": "4041"}, {"propertyName": "4066"}, {"range": "4093", "text": "4041"}, {"propertyName": "4051"}, {"range": "4094", "text": "4041"}, {"propertyName": "4058"}, {"range": "4095", "text": "4041"}, {"propertyName": "4058"}, {"range": "4096", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4097"}, {"range": "4098", "text": "4041"}, "Remove unused variable 'amount_paid'.", {"range": "4099", "text": "4041"}, {"propertyName": "4058"}, {"range": "4100", "text": "4041"}, {"propertyName": "4051"}, {"range": "4101", "text": "4041"}, {"propertyName": "4058"}, {"varName": "4102"}, {"range": "4103", "text": "4041"}, "Remove unused variable 'payment_method'.", {"range": "4104", "text": "4041"}, {"propertyName": "4058"}, {"range": "4105", "text": "4041"}, {"propertyName": "4058"}, {"varName": "4090"}, {"range": "4106", "text": "4041"}, {"range": "4107", "text": "4041"}, {"propertyName": "4058"}, {"range": "4108", "text": "4041"}, {"propertyName": "4051"}, {"range": "4109", "text": "4041"}, {"propertyName": "4066"}, {"range": "4110", "text": "4041"}, {"propertyName": "4051"}, {"range": "4111", "text": "4041"}, {"propertyName": "4058"}, {"range": "4112", "text": "4041"}, {"propertyName": "4051"}, {"range": "4113", "text": "4041"}, {"propertyName": "4058"}, {"range": "4114", "text": "4041"}, {"propertyName": "4051"}, {"range": "4115", "text": "4041"}, {"propertyName": "4058"}, {"range": "4116", "text": "4041"}, {"propertyName": "4051"}, {"range": "4117", "text": "4041"}, {"propertyName": "4058"}, {"range": "4118", "text": "4041"}, {"propertyName": "4051"}, {"range": "4119", "text": "4041"}, {"propertyName": "4051"}, {"range": "4120", "text": "4041"}, {"propertyName": "4051"}, {"range": "4121", "text": "4041"}, {"propertyName": "4051"}, {"range": "4122", "text": "4041"}, {"propertyName": "4051"}, {"range": "4123", "text": "4041"}, {"propertyName": "4058"}, {"range": "4124", "text": "4041"}, {"propertyName": "4051"}, {"range": "4125", "text": "4041"}, {"propertyName": "4051"}, {"range": "4126", "text": "4041"}, {"propertyName": "4058"}, {"range": "4127", "text": "4041"}, {"propertyName": "4058"}, {"range": "4128", "text": "4041"}, {"propertyName": "4058"}, {"range": "4129", "text": "4041"}, {"propertyName": "4051"}, {"range": "4130", "text": "4041"}, {"propertyName": "4051"}, {"range": "4131", "text": "4041"}, {"propertyName": "4058"}, {"range": "4132", "text": "4041"}, {"propertyName": "4051"}, {"range": "4133", "text": "4041"}, {"propertyName": "4051"}, {"range": "4134", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4135"}, {"range": "4136", "text": "4041"}, "Remove unused variable 'request'.", {"range": "4137", "text": "4041"}, {"propertyName": "4058"}, {"range": "4138", "text": "4041"}, {"propertyName": "4058"}, {"range": "4139", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4140"}, {"range": "4141", "text": "4041"}, "Remove unused variable 'Image'.", "replaceWithAlt", {"alt": "4142"}, {"range": "4143", "text": "4144"}, "Replace with `&quot;`.", {"alt": "4145"}, {"range": "4146", "text": "4147"}, "Replace with `&ldquo;`.", {"alt": "4148"}, {"range": "4149", "text": "4150"}, "Replace with `&#34;`.", {"alt": "4151"}, {"range": "4152", "text": "4153"}, "Replace with `&rdquo;`.", {"alt": "4142"}, {"range": "4154", "text": "4155"}, {"alt": "4145"}, {"range": "4156", "text": "4157"}, {"alt": "4148"}, {"range": "4158", "text": "4159"}, {"alt": "4151"}, {"range": "4160", "text": "4161"}, {"varName": "4140"}, {"range": "4162", "text": "4041"}, {"varName": "4163"}, {"range": "4164", "text": "4041"}, "Remove unused variable 'Link'.", {"varName": "4039"}, {"range": "4165", "text": "4041"}, {"varName": "4044"}, {"range": "4166", "text": "4041"}, {"varName": "4167"}, {"range": "4168", "text": "4041"}, "Remove unused variable 'Icon'.", {"varName": "4169"}, {"range": "4170", "text": "4041"}, "Remove unused variable 'FeatureList'.", {"varName": "4140"}, {"range": "4171", "text": "4041"}, {"varName": "4039"}, {"range": "4172", "text": "4041"}, {"varName": "4044"}, {"range": "4173", "text": "4041"}, {"varName": "4039"}, {"range": "4174", "text": "4041"}, {"varName": "4175"}, {"range": "4176", "text": "4041"}, "Remove unused variable 'CardTitle'.", {"varName": "4044"}, {"range": "4177", "text": "4041"}, {"varName": "4046"}, {"range": "4178", "text": "4041"}, {"range": "4179", "text": "4041"}, {"propertyName": "4066"}, {"range": "4180", "text": "4041"}, {"propertyName": "4066"}, {"range": "4181", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4039"}, {"range": "4182", "text": "4041"}, {"varName": "4042"}, {"range": "4183", "text": "4041"}, {"varName": "4044"}, {"range": "4184", "text": "4041"}, {"alt": "4142"}, {"range": "4185", "text": "4142"}, {"alt": "4145"}, {"range": "4186", "text": "4145"}, {"alt": "4148"}, {"range": "4187", "text": "4148"}, {"alt": "4151"}, {"range": "4188", "text": "4151"}, {"alt": "4142"}, {"range": "4189", "text": "4142"}, {"alt": "4145"}, {"range": "4190", "text": "4145"}, {"alt": "4148"}, {"range": "4191", "text": "4148"}, {"alt": "4151"}, {"range": "4192", "text": "4151"}, {"varName": "4046"}, {"range": "4193", "text": "4041"}, {"range": "4194", "text": "4041"}, {"propertyName": "4051"}, {"alt": "4142"}, {"range": "4195", "text": "4196"}, {"alt": "4145"}, {"range": "4197", "text": "4198"}, {"alt": "4148"}, {"range": "4199", "text": "4200"}, {"alt": "4151"}, {"range": "4201", "text": "4202"}, {"alt": "4142"}, {"range": "4203", "text": "4204"}, {"alt": "4145"}, {"range": "4205", "text": "4206"}, {"alt": "4148"}, {"range": "4207", "text": "4208"}, {"alt": "4151"}, {"range": "4209", "text": "4210"}, {"alt": "4142"}, {"range": "4211", "text": "4212"}, {"alt": "4145"}, {"range": "4213", "text": "4214"}, {"alt": "4148"}, {"range": "4215", "text": "4216"}, {"alt": "4151"}, {"range": "4217", "text": "4218"}, {"alt": "4142"}, {"range": "4219", "text": "4220"}, {"alt": "4145"}, {"range": "4221", "text": "4222"}, {"alt": "4148"}, {"range": "4223", "text": "4224"}, {"alt": "4151"}, {"range": "4225", "text": "4226"}, {"varName": "4227"}, {"range": "4228", "text": "4041"}, "Remove unused variable 'generateAdvancedMetadata'.", {"range": "4229", "text": "4041"}, {"propertyName": "4051"}, {"alt": "4142"}, {"range": "4230", "text": "4231"}, {"alt": "4145"}, {"range": "4232", "text": "4233"}, {"alt": "4148"}, {"range": "4234", "text": "4235"}, {"alt": "4151"}, {"range": "4236", "text": "4237"}, {"alt": "4142"}, {"range": "4238", "text": "4239"}, {"alt": "4145"}, {"range": "4240", "text": "4241"}, {"alt": "4148"}, {"range": "4242", "text": "4243"}, {"alt": "4151"}, {"range": "4244", "text": "4245"}, {"alt": "4142"}, {"range": "4246", "text": "4247"}, {"alt": "4145"}, {"range": "4248", "text": "4249"}, {"alt": "4148"}, {"range": "4250", "text": "4251"}, {"alt": "4151"}, {"range": "4252", "text": "4253"}, {"alt": "4142"}, {"range": "4254", "text": "4255"}, {"alt": "4145"}, {"range": "4256", "text": "4257"}, {"alt": "4148"}, {"range": "4258", "text": "4259"}, {"alt": "4151"}, {"range": "4260", "text": "4261"}, {"alt": "4142"}, {"range": "4262", "text": "4263"}, {"alt": "4145"}, {"range": "4264", "text": "4265"}, {"alt": "4148"}, {"range": "4266", "text": "4267"}, {"alt": "4151"}, {"range": "4268", "text": "4269"}, {"alt": "4142"}, {"range": "4270", "text": "4271"}, {"alt": "4145"}, {"range": "4272", "text": "4273"}, {"alt": "4148"}, {"range": "4274", "text": "4275"}, {"alt": "4151"}, {"range": "4276", "text": "4277"}, {"varName": "4163"}, {"range": "4278", "text": "4041"}, {"varName": "4279"}, {"range": "4280", "text": "4041"}, "Remove unused variable 'isDesktop'.", {"varName": "4281"}, {"range": "4282", "text": "4041"}, "Remove unused variable 'isTablet'.", {"varName": "4283"}, {"range": "4284", "text": "4041"}, "Remove unused variable 'UnifiedCard'.", {"varName": "4285"}, {"range": "4286", "text": "4041"}, "Remove unused variable 'TestimonialCard'.", {"varName": "4287"}, {"range": "4288", "text": "4041"}, "Remove unused variable 'SecondaryButton'.", {"varName": "4044"}, {"range": "4289", "text": "4041"}, {"alt": "4142"}, {"range": "4290", "text": "4291"}, {"alt": "4145"}, {"range": "4292", "text": "4293"}, {"alt": "4148"}, {"range": "4294", "text": "4295"}, {"alt": "4151"}, {"range": "4296", "text": "4297"}, {"alt": "4142"}, {"range": "4298", "text": "4299"}, {"alt": "4145"}, {"range": "4300", "text": "4301"}, {"alt": "4148"}, {"range": "4302", "text": "4303"}, {"alt": "4151"}, {"range": "4304", "text": "4305"}, {"varName": "4039"}, {"range": "4306", "text": "4041"}, {"varName": "4042"}, {"range": "4307", "text": "4041"}, {"varName": "4044"}, {"range": "4308", "text": "4041"}, {"varName": "4167"}, {"range": "4309", "text": "4041"}, {"varName": "4310"}, {"range": "4311", "text": "4041"}, "Remove unused variable 'destination'.", {"varName": "4039"}, {"range": "4312", "text": "4041"}, {"varName": "4175"}, {"range": "4313", "text": "4041"}, {"varName": "4044"}, {"range": "4314", "text": "4041"}, {"varName": "4315"}, {"range": "4316", "text": "4041"}, "Remove unused variable 'generateRetreatStructuredData'.", {"varName": "4317"}, {"range": "4318", "text": "4041"}, "Remove unused variable 'destinations'.", {"alt": "4142"}, {"range": "4319", "text": "4320"}, {"alt": "4145"}, {"range": "4321", "text": "4322"}, {"alt": "4148"}, {"range": "4323", "text": "4324"}, {"alt": "4151"}, {"range": "4325", "text": "4326"}, {"alt": "4142"}, {"range": "4327", "text": "4328"}, {"alt": "4145"}, {"range": "4329", "text": "4330"}, {"alt": "4148"}, {"range": "4331", "text": "4332"}, {"alt": "4151"}, {"range": "4333", "text": "4334"}, {"alt": "4142"}, {"range": "4335", "text": "4142"}, {"alt": "4145"}, {"range": "4336", "text": "4145"}, {"alt": "4148"}, {"range": "4337", "text": "4148"}, {"alt": "4151"}, {"range": "4338", "text": "4151"}, {"alt": "4142"}, {"range": "4339", "text": "4142"}, {"alt": "4145"}, {"range": "4340", "text": "4145"}, {"alt": "4148"}, {"range": "4341", "text": "4148"}, {"alt": "4151"}, {"range": "4342", "text": "4151"}, {"varName": "4039"}, {"range": "4343", "text": "4041"}, {"varName": "4044"}, {"range": "4344", "text": "4041"}, {"alt": "4142"}, {"range": "4345", "text": "4142"}, {"alt": "4145"}, {"range": "4346", "text": "4145"}, {"alt": "4148"}, {"range": "4347", "text": "4148"}, {"alt": "4151"}, {"range": "4348", "text": "4151"}, {"alt": "4142"}, {"range": "4349", "text": "4142"}, {"alt": "4145"}, {"range": "4350", "text": "4145"}, {"alt": "4148"}, {"range": "4351", "text": "4148"}, {"alt": "4151"}, {"range": "4352", "text": "4151"}, {"varName": "4039"}, {"range": "4353", "text": "4041"}, {"varName": "4175"}, {"range": "4354", "text": "4041"}, {"varName": "4044"}, {"range": "4355", "text": "4041"}, {"varName": "4046"}, {"range": "4356", "text": "4041"}, {"varName": "4357"}, {"range": "4358", "text": "4041"}, "Remove unused variable 'heroStyles'.", {"varName": "4140"}, {"range": "4359", "text": "4041"}, {"varName": "4046"}, {"range": "4360", "text": "4041"}, {"varName": "4361"}, {"range": "4362", "text": "4041"}, "Remove unused variable 'CTAButton'.", {"varName": "4363"}, {"range": "4364", "text": "4041"}, "Remove unused variable 'FitsseyWidget'.", {"varName": "4365"}, {"range": "4366", "text": "4041"}, "Remove unused variable 'structuredData'.", "Update the dependencies array to be: [isMenuOpen, toggleHighContrast]", {"range": "4367", "text": "4368"}, "Update the dependencies array to be: [ENTERPRISE_TESTS]", {"range": "4369", "text": "4370"}, {"varName": "4371"}, {"range": "4372", "text": "4041"}, "Remove unused variable 'assignment'.", "Update the dependencies array to be: [userVariants, activeTests, testResults, trackConversion]", {"range": "4373", "text": "4374"}, "Update the dependencies array to be: [activeTests, analyzeTestResults, testResults]", {"range": "4375", "text": "4376"}, {"varName": "4377"}, {"range": "4378", "text": "4041"}, "Remove unused variable 'conversionData'.", {"varName": "4379"}, {"range": "4380", "text": "4041"}, "Remove unused variable 'key'.", {"varName": "4381"}, {"range": "4382", "text": "4041"}, "Remove unused variable 'e'.", {"varName": "4381"}, {"range": "4383", "text": "4041"}, "Update the dependencies array to be: [pathname, userSegment, testVariant, trackConversion]", {"range": "4384", "text": "4385"}, {"range": "4386", "text": "4041"}, {"propertyName": "4058"}, {"range": "4387", "text": "4041"}, {"propertyName": "4051"}, {"range": "4388", "text": "4041"}, {"propertyName": "4058"}, {"range": "4389", "text": "4041"}, {"propertyName": "4051"}, {"range": "4390", "text": "4041"}, {"propertyName": "4058"}, {"range": "4391", "text": "4041"}, {"propertyName": "4051"}, {"range": "4392", "text": "4041"}, {"propertyName": "4058"}, {"range": "4393", "text": "4041"}, {"propertyName": "4051"}, {"range": "4394", "text": "4041"}, {"propertyName": "4058"}, {"range": "4395", "text": "4041"}, {"propertyName": "4051"}, {"range": "4396", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4397"}, {"range": "4398", "text": "4041"}, "Remove unused variable 'endTime'.", {"range": "4399", "text": "4041"}, {"propertyName": "4066"}, {"range": "4400", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4401"}, {"range": "4402", "text": "4041"}, "Remove unused variable 'SEARCH_INTENT_MAPPING'.", "Update the dependencies array to be: [TRACKED_KEYWORDS, trackEvent]", {"range": "4403", "text": "4404"}, {"varName": "4405"}, {"range": "4406", "text": "4407"}, "Remove unused variable 'competitor'.", "Update the dependencies array to be: [COMPETITOR_TRACKING, TRACKED_KEYWORDS]", {"range": "4408", "text": "4409"}, "Update the dependencies array to be: [TRACKED_KEYWORDS, rankingData, trackEvent]", {"range": "4410", "text": "4411"}, "Update the dependencies array to be: [rankingData, competitorData, trackEvent]", {"range": "4412", "text": "4413"}, "Update the dependencies array to be: [rankingData, competitorData, keywordPerformance, seoMetrics, trackEvent]", {"range": "4414", "text": "4415"}, {"range": "4416", "text": "4041"}, {"propertyName": "4058"}, {"range": "4417", "text": "4041"}, {"propertyName": "4051"}, {"range": "4418", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4419"}, {"range": "4420", "text": "4041"}, "Remove unused variable 'router'.", {"varName": "4421"}, {"range": "4422", "text": "4041"}, "Remove unused variable 'useMemo'.", {"varName": "4039"}, {"range": "4423", "text": "4041"}, {"varName": "4042"}, {"range": "4424", "text": "4041"}, {"varName": "4044"}, {"range": "4425", "text": "4041"}, {"varName": "4046"}, {"range": "4426", "text": "4041"}, {"range": "4427", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4428"}, {"range": "4429", "text": "4041"}, "Remove unused variable 'showBookingForm'.", {"varName": "4046"}, {"range": "4430", "text": "4041"}, {"varName": "4431"}, {"range": "4432", "text": "4041"}, "Remove unused variable 'pathname'.", {"varName": "4046"}, {"range": "4433", "text": "4041"}, {"varName": "4051"}, {"range": "4434", "text": "4041"}, "Remove unused variable 'error'.", {"range": "4435", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4051"}, {"range": "4436", "text": "4041"}, {"varName": "4051"}, {"range": "4437", "text": "4041"}, {"varName": "4438"}, {"range": "4439", "text": "4041"}, "Remove unused variable 'errorInfo'.", {"varName": "4046"}, {"range": "4440", "text": "4041"}, {"range": "4441", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4046"}, {"range": "4442", "text": "4041"}, {"varName": "4140"}, {"range": "4443", "text": "4041"}, {"varName": "4039"}, {"range": "4444", "text": "4041"}, {"varName": "4042"}, {"range": "4445", "text": "4041"}, {"varName": "4044"}, {"range": "4446", "text": "4041"}, {"varName": "4046"}, {"range": "4447", "text": "4041"}, {"varName": "4140"}, {"range": "4448", "text": "4041"}, {"alt": "4142"}, {"range": "4449", "text": "4450"}, {"alt": "4145"}, {"range": "4451", "text": "4452"}, {"alt": "4148"}, {"range": "4453", "text": "4454"}, {"alt": "4151"}, {"range": "4455", "text": "4456"}, {"alt": "4142"}, {"range": "4457", "text": "4458"}, {"alt": "4145"}, {"range": "4459", "text": "4460"}, {"alt": "4148"}, {"range": "4461", "text": "4462"}, {"alt": "4151"}, {"range": "4463", "text": "4464"}, {"alt": "4142"}, {"range": "4465", "text": "4466"}, {"alt": "4145"}, {"range": "4467", "text": "4468"}, {"alt": "4148"}, {"range": "4469", "text": "4470"}, {"alt": "4151"}, {"range": "4471", "text": "4472"}, {"alt": "4142"}, {"range": "4473", "text": "4474"}, {"alt": "4145"}, {"range": "4475", "text": "4476"}, {"alt": "4148"}, {"range": "4477", "text": "4478"}, {"alt": "4151"}, {"range": "4479", "text": "4480"}, {"varName": "4481"}, {"range": "4482", "text": "4041"}, "Remove unused variable 'latestPosts'.", {"varName": "4039"}, {"range": "4483", "text": "4041"}, {"varName": "4042"}, {"range": "4484", "text": "4041"}, {"varName": "4044"}, {"range": "4485", "text": "4041"}, {"varName": "4046"}, {"range": "4486", "text": "4041"}, {"varName": "4487"}, {"range": "4488", "text": "4041"}, "Remove unused variable 'currentPost'.", {"varName": "4489"}, {"range": "4490", "text": "4041"}, "Remove unused variable 'allPosts'.", {"varName": "4487"}, {"range": "4491", "text": "4041"}, {"varName": "4489"}, {"range": "4492", "text": "4041"}, {"varName": "4493"}, {"range": "4494", "text": "4041"}, "Remove unused variable 'variant'.", {"range": "4495", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4496"}, {"range": "4497", "text": "4041"}, "Remove unused variable 'index'.", {"range": "4498", "text": "4041"}, {"propertyName": "4066"}, {"alt": "4142"}, {"range": "4499", "text": "4500"}, {"alt": "4145"}, {"range": "4501", "text": "4502"}, {"alt": "4148"}, {"range": "4503", "text": "4504"}, {"alt": "4151"}, {"range": "4505", "text": "4506"}, {"alt": "4142"}, {"range": "4507", "text": "4508"}, {"alt": "4145"}, {"range": "4509", "text": "4510"}, {"alt": "4148"}, {"range": "4511", "text": "4512"}, {"alt": "4151"}, {"range": "4513", "text": "4514"}, {"varName": "4515"}, {"range": "4516", "text": "4041"}, "Remove unused variable 'useMotionValue'.", {"varName": "4517"}, {"range": "4518", "text": "4041"}, "Remove unused variable 'useSpring'.", {"varName": "4519"}, {"range": "4520", "text": "4041"}, "Remove unused variable 'intensity'.", {"range": "4521", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4522"}, {"range": "4523", "text": "4041"}, "Remove unused variable 'animated'.", {"varName": "4493"}, {"range": "4524", "text": "4041"}, {"varName": "4496"}, {"range": "4525", "text": "4041"}, {"alt": "4142"}, {"range": "4526", "text": "4527"}, {"alt": "4145"}, {"range": "4528", "text": "4529"}, {"alt": "4148"}, {"range": "4530", "text": "4531"}, {"alt": "4151"}, {"range": "4532", "text": "4533"}, {"alt": "4142"}, {"range": "4534", "text": "4527"}, {"alt": "4145"}, {"range": "4535", "text": "4529"}, {"alt": "4148"}, {"range": "4536", "text": "4531"}, {"alt": "4151"}, {"range": "4537", "text": "4533"}, {"varName": "4538"}, {"range": "4539", "text": "4041"}, "Remove unused variable 'testimonialId'.", {"varName": "4540"}, {"range": "4541", "text": "4041"}, "Remove unused variable 'position'.", {"varName": "4542"}, {"range": "4543", "text": "4041"}, "Remove unused variable 'useCallback'.", {"varName": "4421"}, {"range": "4544", "text": "4041"}, {"varName": "4046"}, {"range": "4545", "text": "4041"}, {"varName": "4546"}, {"range": "4547", "text": "4041"}, "Remove unused variable 'NavLink'.", {"varName": "4548"}, {"range": "4549", "text": "4041"}, "Remove unused variable 'mainNavItems'.", {"varName": "4550"}, {"range": "4551", "text": "4041"}, "Remove unused variable 'isVisible'.", {"varName": "4552"}, {"range": "4553", "text": "4041"}, "Remove unused variable 'handleDropdownToggle'.", {"varName": "4554"}, {"range": "4555", "text": "4041"}, "Remove unused variable 'handleMouseEnter'.", {"varName": "4556"}, {"range": "4557", "text": "4041"}, "Remove unused variable 'handleMouseLeave'.", {"varName": "4496"}, {"range": "4558", "text": "4041"}, {"varName": "4046"}, {"range": "4559", "text": "4041"}, {"varName": "4560"}, {"range": "4561", "text": "4041"}, "Remove unused variable 'src'.", {"varName": "4562"}, {"range": "4563", "text": "4041"}, "Remove unused variable 'alt'.", {"varName": "4564"}, {"range": "4565", "text": "4041"}, "Remove unused variable 'props'.", "Update the dependencies array to be: []", {"range": "4566", "text": "4567"}, {"range": "4568", "text": "4041"}, {"propertyName": "4051"}, "Update the dependencies array to be: [pathname, trackMetric]", {"range": "4569", "text": "4570"}, {"varName": "4571"}, {"range": "4572", "text": "4041"}, "Remove unused variable 'event'.", {"range": "4573", "text": "4041"}, {"propertyName": "4051"}, {"range": "4574", "text": "4041"}, {"propertyName": "4066"}, {"range": "4575", "text": "4041"}, {"propertyName": "4066"}, {"range": "4576", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4577"}, {"range": "4578", "text": "4041"}, "Remove unused variable 'useState'.", {"varName": "4579"}, {"range": "4580", "text": "4041"}, "Remove unused variable 'useEffect'.", {"range": "4581", "text": "4041"}, {"propertyName": "4058"}, {"range": "4582", "text": "4041"}, {"propertyName": "4051"}, {"range": "4583", "text": "4041"}, {"propertyName": "4058"}, {"range": "4584", "text": "4041"}, {"propertyName": "4058"}, {"range": "4585", "text": "4041"}, {"propertyName": "4051"}, {"range": "4586", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4587"}, {"range": "4588", "text": "4041"}, "Remove unused variable 'setUpdateAvailable'.", {"varName": "4589"}, {"range": "4590", "text": "4041"}, "Remove unused variable 'CACHE_NAME'.", {"varName": "4591"}, {"range": "4592", "text": "4041"}, "Remove unused variable 'CACHE_STRATEGIES'.", {"range": "4593", "text": "4041"}, {"propertyName": "4058"}, {"range": "4594", "text": "4041"}, {"propertyName": "4058"}, {"range": "4595", "text": "4041"}, {"propertyName": "4058"}, {"range": "4596", "text": "4041"}, {"propertyName": "4058"}, {"range": "4597", "text": "4041"}, {"propertyName": "4058"}, {"range": "4598", "text": "4041"}, {"propertyName": "4051"}, {"range": "4599", "text": "4041"}, {"propertyName": "4051"}, {"range": "4600", "text": "4041"}, {"propertyName": "4051"}, {"range": "4601", "text": "4041"}, {"propertyName": "4051"}, {"range": "4602", "text": "4041"}, {"propertyName": "4051"}, {"range": "4603", "text": "4041"}, {"propertyName": "4058"}, {"range": "4604", "text": "4041"}, {"propertyName": "4058"}, {"range": "4605", "text": "4041"}, {"propertyName": "4051"}, {"range": "4606", "text": "4041"}, {"propertyName": "4058"}, {"range": "4607", "text": "4041"}, {"propertyName": "4051"}, {"range": "4608", "text": "4041"}, {"propertyName": "4058"}, {"range": "4609", "text": "4041"}, {"propertyName": "4058"}, {"range": "4610", "text": "4041"}, {"propertyName": "4058"}, {"range": "4611", "text": "4041"}, {"propertyName": "4051"}, {"range": "4612", "text": "4041"}, {"propertyName": "4058"}, {"range": "4613", "text": "4041"}, {"propertyName": "4051"}, {"alt": "4142"}, {"range": "4614", "text": "4615"}, {"alt": "4145"}, {"range": "4616", "text": "4617"}, {"alt": "4148"}, {"range": "4618", "text": "4619"}, {"alt": "4151"}, {"range": "4620", "text": "4621"}, {"alt": "4142"}, {"range": "4622", "text": "4623"}, {"alt": "4145"}, {"range": "4624", "text": "4625"}, {"alt": "4148"}, {"range": "4626", "text": "4627"}, {"alt": "4151"}, {"range": "4628", "text": "4629"}, {"varName": "4519"}, {"range": "4630", "text": "4041"}, {"varName": "4227"}, {"range": "4631", "text": "4041"}, {"varName": "4632"}, {"range": "4633", "text": "4041"}, "Remove unused variable 'competitorData'.", {"varName": "4634"}, {"range": "4635", "text": "4041"}, "Remove unused variable 'setCompetitorData'.", {"varName": "4636"}, {"range": "4637", "text": "4041"}, "Remove unused variable 'rankingData'.", {"varName": "4638"}, {"range": "4639", "text": "4041"}, "Remove unused variable 'keywordGaps'.", {"varName": "4640"}, {"range": "4641", "text": "4041"}, "Remove unused variable 'contentGaps'.", {"varName": "4642"}, {"range": "4643", "text": "4041"}, "Remove unused variable 'COMPETITORS'.", {"varName": "4644"}, {"range": "4645", "text": "4041"}, "Remove unused variable 'BACKLINK_OPPORTUNITIES'.", {"varName": "4646"}, {"range": "4647", "text": "4041"}, "Remove unused variable 'TECHNICAL_GAPS'.", "Update the dependencies array to be: [CONTENT_OPPORTUNITIES.missingPages, KEYWORD_OPPORTUNITIES.highVolumeLowCompetition]", {"range": "4648", "text": "4649"}, {"varName": "4650"}, {"range": "4651", "text": "4041"}, "Remove unused variable 'DOMINATION_STRATEGIES'.", "Update the dependencies array to be: [title, description, image, url, type, siteKeywords, siteName, siteDescription, siteUrl, siteImage]", {"range": "4652", "text": "4653"}, {"varName": "4654"}, {"range": "4655", "text": "4041"}, "Remove unused variable 'className'.", {"varName": "4496"}, {"range": "4656", "text": "4041"}, {"alt": "4142"}, {"range": "4657", "text": "4658"}, {"alt": "4145"}, {"range": "4659", "text": "4660"}, {"alt": "4148"}, {"range": "4661", "text": "4662"}, {"alt": "4151"}, {"range": "4663", "text": "4664"}, {"alt": "4142"}, {"range": "4665", "text": "4658"}, {"alt": "4145"}, {"range": "4666", "text": "4660"}, {"alt": "4148"}, {"range": "4667", "text": "4662"}, {"alt": "4151"}, {"range": "4668", "text": "4664"}, {"alt": "4142"}, {"range": "4669", "text": "4670"}, {"alt": "4145"}, {"range": "4671", "text": "4672"}, {"alt": "4148"}, {"range": "4673", "text": "4674"}, {"alt": "4151"}, {"range": "4675", "text": "4676"}, {"alt": "4142"}, {"range": "4677", "text": "4670"}, {"alt": "4145"}, {"range": "4678", "text": "4672"}, {"alt": "4148"}, {"range": "4679", "text": "4674"}, {"alt": "4151"}, {"range": "4680", "text": "4676"}, {"varName": "4046"}, {"range": "4681", "text": "4041"}, {"varName": "4682"}, {"range": "4683", "text": "4041"}, "Remove unused variable 'rates'.", "Update the dependencies array to be: [fetchExchangeRates, fromCurrency, toCurrency]", {"range": "4684", "text": "4685"}, "Update the dependencies array to be: [inputAmount, exchangeRate, calculateConversion]", {"range": "4686", "text": "4687"}, {"range": "4688", "text": "4041"}, {"propertyName": "4051"}, {"varName": "4689"}, {"range": "4690", "text": "4041"}, "Remove unused variable 'climate'.", {"varName": "4691"}, {"range": "4692", "text": "4041"}, "Remove unused variable 'bestTime'.", {"varName": "4693"}, {"range": "4694", "text": "4041"}, "Remove unused variable 'culture'.", {"varName": "4039"}, {"range": "4695", "text": "4041"}, {"varName": "4175"}, {"range": "4696", "text": "4041"}, {"varName": "4044"}, {"range": "4697", "text": "4041"}, {"alt": "4142"}, {"range": "4698", "text": "4699"}, {"alt": "4145"}, {"range": "4700", "text": "4701"}, {"alt": "4148"}, {"range": "4702", "text": "4703"}, {"alt": "4151"}, {"range": "4704", "text": "4705"}, {"alt": "4142"}, {"range": "4706", "text": "4707"}, {"alt": "4145"}, {"range": "4708", "text": "4709"}, {"alt": "4148"}, {"range": "4710", "text": "4711"}, {"alt": "4151"}, {"range": "4712", "text": "4713"}, {"alt": "4142"}, {"range": "4714", "text": "4715"}, {"alt": "4145"}, {"range": "4716", "text": "4717"}, {"alt": "4148"}, {"range": "4718", "text": "4719"}, {"alt": "4151"}, {"range": "4720", "text": "4721"}, {"alt": "4142"}, {"range": "4722", "text": "4723"}, {"alt": "4145"}, {"range": "4724", "text": "4725"}, {"alt": "4148"}, {"range": "4726", "text": "4727"}, {"alt": "4151"}, {"range": "4728", "text": "4729"}, {"alt": "4142"}, {"range": "4730", "text": "4731"}, {"alt": "4145"}, {"range": "4732", "text": "4733"}, {"alt": "4148"}, {"range": "4734", "text": "4735"}, {"alt": "4151"}, {"range": "4736", "text": "4737"}, {"varName": "4738"}, {"range": "4739", "text": "4041"}, "Remove unused variable 'type'.", {"varName": "4051"}, {"range": "4740", "text": "4041"}, {"range": "4741", "text": "4041"}, {"propertyName": "4051"}, {"range": "4742", "text": "4041"}, {"propertyName": "4066"}, {"varName": "4743"}, {"range": "4744", "text": "4041"}, "Remove unused variable 'quality'.", {"varName": "4745"}, {"range": "4746", "text": "4041"}, "Remove unused variable 'sizes'.", {"varName": "4747"}, {"range": "4748", "text": "4041"}, "Remove unused variable 'direction'.", {"varName": "4749"}, {"range": "4750", "text": "4041"}, "Remove unused variable 'onClick'.", {"varName": "4564"}, {"range": "4751", "text": "4041"}, {"varName": "4752"}, {"range": "4753", "text": "4041"}, "Remove unused variable 'scale'.", {"varName": "4754"}, {"range": "4755", "text": "4041"}, "Remove unused variable 'breakpoints'.", {"varName": "4756"}, {"range": "4757", "text": "4041"}, "Remove unused variable 'scrollRef'.", {"range": "4758", "text": "4041"}, {"propertyName": "4066"}, {"alt": "4142"}, {"range": "4759", "text": "4715"}, {"alt": "4145"}, {"range": "4760", "text": "4717"}, {"alt": "4148"}, {"range": "4761", "text": "4719"}, {"alt": "4151"}, {"range": "4762", "text": "4721"}, {"alt": "4142"}, {"range": "4763", "text": "4142"}, {"alt": "4145"}, {"range": "4764", "text": "4145"}, {"alt": "4148"}, {"range": "4765", "text": "4148"}, {"alt": "4151"}, {"range": "4766", "text": "4151"}, {"varName": "4767"}, {"range": "4768", "text": "4041"}, "Remove unused variable 'timeOfDay'.", {"range": "4769", "text": "4041"}, {"propertyName": "4066"}, {"alt": "4142"}, {"range": "4770", "text": "4771"}, {"alt": "4145"}, {"range": "4772", "text": "4773"}, {"alt": "4148"}, {"range": "4774", "text": "4775"}, {"alt": "4151"}, {"range": "4776", "text": "4777"}, {"varName": "4778"}, {"range": "4779", "text": "4041"}, "Remove unused variable 'Calendar'.", {"varName": "4780"}, {"range": "4781", "text": "4041"}, "Remove unused variable 'User'.", {"varName": "4782"}, {"range": "4783", "text": "4041"}, "Remove unused variable 'Tag'.", {"varName": "4784"}, {"range": "4785", "text": "4041"}, "Remove unused variable 'Palette'.", {"varName": "4786"}, {"range": "4787", "text": "4041"}, "Remove unused variable 'Sunrise'.", {"varName": "4788"}, {"range": "4789", "text": "4041"}, "Remove unused variable 'Sunset'.", {"varName": "4790"}, {"range": "4791", "text": "4041"}, "Remove unused variable 'Mountain'.", {"varName": "4792"}, {"range": "4793", "text": "4041"}, "Remove unused variable 'Droplet'.", {"varName": "4794"}, {"range": "4795", "text": "4041"}, "Remove unused variable 'Heart'.", {"varName": "4796"}, {"range": "4797", "text": "4041"}, "Remove unused variable 'MapPin'.", {"varName": "4798"}, {"range": "4799", "text": "4041"}, "Remove unused variable 'Sparkles'.", {"varName": "4778"}, {"range": "4800", "text": "4041"}, {"varName": "4780"}, {"range": "4801", "text": "4041"}, {"varName": "4782"}, {"range": "4802", "text": "4041"}, {"varName": "4784"}, {"range": "4803", "text": "4041"}, {"varName": "4788"}, {"range": "4804", "text": "4041"}, {"varName": "4790"}, {"range": "4805", "text": "4041"}, {"varName": "4792"}, {"range": "4806", "text": "4041"}, {"varName": "4796"}, {"range": "4807", "text": "4041"}, {"varName": "4798"}, {"range": "4808", "text": "4041"}, {"varName": "4809"}, {"range": "4810", "text": "4041"}, "Remove unused variable 'setOffset'.", {"range": "4811", "text": "4041"}, {"propertyName": "4051"}, {"range": "4812", "text": "4041"}, {"propertyName": "4051"}, {"range": "4813", "text": "4041"}, {"propertyName": "4051"}, {"range": "4814", "text": "4041"}, {"propertyName": "4066"}, {"range": "4815", "text": "4041"}, {"propertyName": "4051"}, {"range": "4816", "text": "4041"}, {"propertyName": "4066"}, {"range": "4817", "text": "4041"}, {"propertyName": "4058"}, {"varName": "4818"}, {"range": "4819", "text": "4407"}, "Remove unused variable 'keyword'.", {"varName": "4738"}, {"range": "4820", "text": "4041"}, {"range": "4821", "text": "4041"}, {"propertyName": "4058"}, {"range": "4822", "text": "4041"}, {"propertyName": "4058"}, {"range": "4823", "text": "4041"}, {"propertyName": "4058"}, {"range": "4824", "text": "4041"}, {"propertyName": "4058"}, {"range": "4825", "text": "4041"}, {"propertyName": "4051"}, {"range": "4826", "text": "4041"}, {"propertyName": "4058"}, {"range": "4827", "text": "4041"}, {"propertyName": "4051"}, {"range": "4828", "text": "4041"}, {"propertyName": "4058"}, {"range": "4829", "text": "4041"}, {"propertyName": "4051"}, {"range": "4830", "text": "4041"}, {"propertyName": "4058"}, {"range": "4831", "text": "4041"}, {"propertyName": "4051"}, {"range": "4832", "text": "4041"}, {"propertyName": "4051"}, {"range": "4833", "text": "4041"}, {"propertyName": "4058"}, {"range": "4834", "text": "4041"}, {"propertyName": "4051"}, {"range": "4835", "text": "4041"}, {"propertyName": "4066"}, {"range": "4836", "text": "4041"}, {"propertyName": "4058"}, {"range": "4837", "text": "4041"}, {"propertyName": "4051"}, {"range": "4838", "text": "4041"}, {"propertyName": "4051"}, {"range": "4839", "text": "4041"}, {"propertyName": "4051"}, "<PERSON><PERSON><PERSON><PERSON>", [116, 126], "", "SectionTitle", [125, 141], "BodyText", [154, 166], "UnifiedButton", [219, 238], [768, 776], "[router, verifyTokenAndLoadData]", [1272, 1322], "error", [1640, 1689], [2223, 2273], [233, 252], [1201, 1252], [2883, 2933], [4205, 4267], "log", [4434, 4485], [2502, 2551], [4339, 4390], [5494, 5546], "crypto", [7, 19], [653, 719], "warn", [894, 960], [1048, 1099], [1200, 1283], [2533, 2617], [3412, 3502], [3961, 4051], [5024, 5135], [5878, 5921], [6378, 6431], "hashPassword", [6502, 6618], [1594, 1647], [1802, 1852], "validateSchema", [82, 100], "ValidationError", [100, 119], "handleApiError", [137, 155], [1697, 1770], "emailSent", [4546, 4612], [5104, 5153], "emailContent", [5660, 6064], [318, 408], [765, 817], [996, 1064], [1491, 1543], [1684, 1731], "amount_paid", [2162, 2179], [2196, 2307], [3218, 3276], [3446, 3564], "payment_method", [3856, 3872], [3886, 3985], [4272, 4413], [4760, 5395], [5399, 5453], [6132, 6194], [643, 705], [1846, 1895], [1848, 1908], [1952, 1996], [3378, 3435], [3526, 3569], [5235, 5302], [5397, 5444], [6053, 6109], [7810, 7867], [7927, 7982], [8046, 8109], [8182, 8236], [9557, 9615], [1039, 1117], [1390, 1444], [3323, 3402], [3687, 3757], [982, 1038], [1043, 1101], [1456, 1517], [1711, 1760], [3242, 3296], [3321, 3382], [4467, 4528], [1595, 1644], "request", [1949, 1956], [825, 890], [897, 1003], [1294, 1345], "Image", [52, 63], "&quot;", [10353, 10413], "\n              &quot;Każda historia ma swoją moc...\"\n            ", "&ldquo;", [10353, 10413], "\n              &ldquo;Każda historia ma swoją moc...\"\n            ", "&#34;", [10353, 10413], "\n              &#34;Każda historia ma swoją moc...\"\n            ", "&rdquo;", [10353, 10413], "\n              &rdquo;Każda historia ma swoją moc...\"\n            ", [10353, 10413], "\n              \"Każda historia ma swoją moc...&quot;\n            ", [10353, 10413], "\n              \"Każda historia ma swoją moc...&ldquo;\n            ", [10353, 10413], "\n              \"Każda historia ma swoją moc...&#34;\n            ", [10353, 10413], "\n              \"Każda historia ma swoją moc...&rdquo;\n            ", [7, 18], "Link", [39, 49], [101, 111], [139, 151], "Icon", [197, 247], "FeatureList", [332, 347], [7, 18], [101, 111], [139, 151], [145, 155], "CardTitle", [170, 183], [183, 195], [87, 106], [292, 339], [379, 421], [469, 512], [71, 81], [80, 96], [109, 121], [16957, 16958], [16957, 16958], [16957, 16958], [16957, 16958], [16976, 16977], [16976, 16977], [16976, 16977], [16976, 16977], [63, 82], [3659, 3690], [11856, 11926], "\n            &quot;Każda podróż zaczyna się od jednego kroku...\"\n          ", [11856, 11926], "\n            &ldquo;Każda podróż zaczyna się od jednego kroku...\"\n          ", [11856, 11926], "\n            &#34;Każda podróż zaczyna się od jednego kroku...\"\n          ", [11856, 11926], "\n            &rdquo;Każda podróż zaczyna się od jednego kroku...\"\n          ", [11856, 11926], "\n            \"Każda podróż zaczyna się od jednego kroku...&quot;\n          ", [11856, 11926], "\n            \"Każda podróż zaczyna się od jednego kroku...&ldquo;\n          ", [11856, 11926], "\n            \"Każda podróż zaczyna się od jednego kroku...&#34;\n          ", [11856, 11926], "\n            \"Każda podróż zaczyna się od jednego kroku...&rdquo;\n          ", [2760, 2836], "\n              &quot;Najdłuższa podróż zaczyna się od jednego kroku\"\n            ", [2760, 2836], "\n              &ldquo;Najdłuższa podróż zaczyna się od jednego kroku\"\n            ", [2760, 2836], "\n              &#34;Najdłuższa podróż zaczyna się od jednego kroku\"\n            ", [2760, 2836], "\n              &rdquo;Najdłuższa podróż zaczyna się od jednego kroku\"\n            ", [2760, 2836], "\n              \"Najdłuższa podróż zaczyna się od jednego kroku&quot;\n            ", [2760, 2836], "\n              \"Najdłuższa podróż zaczyna się od jednego kroku&ldquo;\n            ", [2760, 2836], "\n              \"Najdłuższ<PERSON> podróż zaczyna się od jednego kroku&#34;\n            ", [2760, 2836], "\n              \"Najdłuższa podróż zaczyna się od jednego kroku&rdquo;\n            ", "generateAdvancedMetadata", [1021, 1082], [5700, 5763], [2689, 2732], "\n              &quot;Tidak apa-apa\"\n            ", [2689, 2732], "\n              &ldquo;Tidak apa-apa\"\n            ", [2689, 2732], "\n              &#34;Tidak apa-apa\"\n            ", [2689, 2732], "\n              &rdquo;Tidak apa-apa\"\n            ", [2689, 2732], "\n              \"Tidak apa-apa&quot;\n            ", [2689, 2732], "\n              \"Tidak apa-apa&ldquo;\n            ", [2689, 2732], "\n              \"Tidak apa-apa&#34;\n            ", [2689, 2732], "\n              \"Tidak apa-apa&rdquo;\n            ", [2790, 2863], "\n              (To z<PERSON>zy &quot;<PERSON><PERSON>\" po indonezyjsku)\n            ", [2790, 2863], "\n              (To z<PERSON><PERSON> &ldquo;<PERSON><PERSON>\" po indonezyjsku)\n            ", [2790, 2863], "\n              (To z<PERSON><PERSON> &#34;<PERSON><PERSON> <PERSON>\" po indonezyjsku)\n            ", [2790, 2863], "\n              (To z<PERSON><PERSON> &rdquo;<PERSON><PERSON> <PERSON>\" po indonezyjsku)\n            ", [2790, 2863], "\n              (To <PERSON><PERSON><PERSON> \"Nie ma problemu&quot; po indonezyjsku)\n            ", [2790, 2863], "\n              (To <PERSON><PERSON><PERSON> \"<PERSON>e ma <PERSON>u&ldquo; po indonezyjsku)\n            ", [2790, 2863], "\n              (<PERSON> <PERSON><PERSON><PERSON> \"<PERSON><PERSON> ma <PERSON>u&#34; po indonezy<PERSON><PERSON><PERSON>)\n            ", [2790, 2863], "\n              (To <PERSON><PERSON><PERSON> \"<PERSON>e ma <PERSON>u&rdquo; po indonezyjsku)\n            ", [4286, 4372], "\n              &quot;Czasami trzeba si<PERSON> z<PERSON>, ż<PERSON><PERSON> odnaleźć prawdziwą drogę\"\n            ", [4286, 4372], "\n              &ldquo;Czasami trzeba si<PERSON> z<PERSON>, ż<PERSON><PERSON> odnaleźć prawdziwą drogę\"\n            ", [4286, 4372], "\n              &#34;Czasami trzeba si<PERSON>, <PERSON><PERSON><PERSON> odn<PERSON> prawdziwą drogę\"\n            ", [4286, 4372], "\n              &rdquo;Czasami trzeba si<PERSON> z<PERSON>, ż<PERSON><PERSON> odnale<PERSON> prawdziwą drogę\"\n            ", [4286, 4372], "\n              \"Czasami trzeba si<PERSON> z<PERSON>, <PERSON><PERSON><PERSON> odnale<PERSON> prawdziwą drogę&quot;\n            ", [4286, 4372], "\n              \"Czasami trzeba si<PERSON> z<PERSON>, ż<PERSON><PERSON> odnale<PERSON>ć prawdziwą drogę&ldquo;\n            ", [4286, 4372], "\n              \"Czasami trzeba si<PERSON> z<PERSON>, <PERSON><PERSON><PERSON> odn<PERSON> prawdziwą drogę&#34;\n            ", [4286, 4372], "\n              \"Czasami trzeba si<PERSON> z<PERSON>, ż<PERSON><PERSON> odnale<PERSON>ć prawdziwą drogę&rdquo;\n            ", [81, 91], "isDesktop", [380, 390], "isTablet", [389, 399], "UnifiedCard", [243, 255], "TestimonialCard", [271, 290], "SecondaryButton", [330, 394], [77, 89], [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą &quot;Fly with Bakasana\".\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą &ldquo;Fly with Bakasana\".\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą &#34;Fly with Bakasana\".\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą &rdquo;Fly with Bakasana\".\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą \"Fly with Bakasana&quot;.\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą \"Fly with Bakasana&ldquo;.\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą \"Fly with Bakasana&#34;.\n            ", [1665, 1759], ", prowadząca działalność\n              gospodarczą pod nazwą \"Fly with Bakasana&rdquo;.\n            ", [144, 154], [153, 169], [169, 181], [252, 302], "destination", [1149, 1191], [101, 111], [126, 139], [139, 151], "generateRetreatStructuredData", [255, 328], "destinations", [356, 370], [14431, 14552], "\n            &quot;Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...\"\n          ", [14431, 14552], "\n            &ldquo;Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...\"\n          ", [14431, 14552], "\n            &#34;Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...\"\n          ", [14431, 14552], "\n            &rdquo;Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...\"\n          ", [14431, 14552], "\n            \"Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...&quot;\n          ", [14431, 14552], "\n            \"Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...&ldquo;\n          ", [14431, 14552], "\n            \"Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...&#34;\n          ", [14431, 14552], "\n            \"Każda podróż zaczyna się od jednego kroku, każda transformacja od\n            jednej decyzji...&rdquo;\n          ", [12457, 12458], [12457, 12458], [12457, 12458], [12457, 12458], [12476, 12477], [12476, 12477], [12476, 12477], [12476, 12477], [319, 329], [353, 363], [14404, 14405], [14404, 14405], [14404, 14405], [14404, 14405], [14424, 14425], [14424, 14425], [14424, 14425], [14424, 14425], [294, 304], [317, 328], [328, 338], [54, 73], "hero<PERSON><PERSON><PERSON>", [335, 399], [24, 35], [300, 314], "CTAButton", [317, 327], "FitsseyWidget", [437, 452], "structuredData", [701, 1122], [3618, 3630], "[is<PERSON><PERSON>u<PERSON><PERSON>, toggleHighContrast]", [8685, 8687], "[ENTERPRISE_TESTS]", "assignment", [12187, 12199], [12342, 12382], "[userVariants, activeTests, testResults, trackConversion]", [15518, 15544], "[activeTests, analyzeTestResults, testResults]", "conversionData", [9188, 9202], "key", [11656, 11659], "e", [14272, 14275], [14627, 14630], [15371, 15407], "[pathname, userSegment, testVariant, trackConversion]", [3143, 3182], [3242, 3295], [6685, 6714], [6768, 6811], [10379, 10408], [10462, 10505], [14011, 14042], [14096, 14141], [15235, 15272], [15326, 15377], [2376, 2419], "endTime", [7352, 7386], [328, 474], [703, 760], "SEARCH_INTENT_MAPPING", [5384, 6129], [8331, 8333], "[TRACKED_KEYWORDS, trackEvent]", "competitor", [9801, 9811], "()", [10903, 10905], "[COMPETITOR_TRACKING, TRACKED_KEYWORDS]", [14542, 14555], "[TRACKED_KEYWORDS, rankingData, trackEvent]", [17017, 17046], "[ranking<PERSON><PERSON>, competitorData, trackEvent]", [17445, 17506], "[rankingData, competitorData, keywordPerformance, seoMetrics, trackEvent]", [796, 840], [14572, 14613], [15682, 15730], "router", [17182, 17209], "useMemo", [32, 41], [139, 149], [148, 162], [173, 183], [236, 255], [790, 844], "showBookingForm", [2577, 2592], [104, 123], "pathname", [167, 198], [97, 116], [285, 290], [477, 538], [222, 227], [287, 293], "errorInfo", [292, 303], [207, 221], [1284, 1338], [126, 145], [66, 77], [54, 64], [63, 79], [92, 104], [63, 82], [21, 32], [1436, 1462], "&quot;<PERSON><PERSON> to powrót do siebie\"", [1436, 1462], "&ldquo;Joga to powrót do siebie\"", [1436, 1462], "&#34;<PERSON><PERSON> to powrót do siebie\"", [1436, 1462], "&rdquo;Joga to powrót do siebie\"", [1436, 1462], "\"Joga to powrót do siebie&quot;", [1436, 1462], "\"Joga to powrót do siebie&ldquo;", [1436, 1462], "\"Joga to powrót do siebie&#34;", [1436, 1462], "\"Joga to powrót do siebie&rdquo;", [8684, 8754], "\n            &quot;<PERSON><PERSON> to nie t<PERSON><PERSON>, to spos<PERSON>b <PERSON>\"\n          ", [8684, 8754], "\n            &ldquo;<PERSON>ga to nie t<PERSON><PERSON>, to sposób życia\"\n          ", [8684, 8754], "\n            &#34;<PERSON><PERSON> to nie t<PERSON><PERSON>, to spos<PERSON><PERSON> ż<PERSON>\"\n          ", [8684, 8754], "\n            &rdquo;<PERSON>ga to nie t<PERSON><PERSON>, to sposób życia\"\n          ", [8684, 8754], "\n            \"Joga to nie t<PERSON><PERSON>, to spos<PERSON><PERSON>&quot;\n          ", [8684, 8754], "\n            \"Joga to nie t<PERSON><PERSON>, to sposób życia&ldquo;\n          ", [8684, 8754], "\n            \"<PERSON>ga to nie t<PERSON><PERSON>, to spos<PERSON>b życia&#34;\n          ", [8684, 8754], "\n            \"Joga to nie t<PERSON><PERSON>, to sposób życia&rdquo;\n          ", "latestPosts", [16257, 16272], [143, 153], [152, 166], [177, 187], [240, 259], "currentPost", [1789, 1801], "allPosts", [1800, 1810], [1789, 1801], [1800, 1810], "variant", [1197, 1217], [1487, 1549], "index", [2977, 2984], [3103, 3164], [5900, 5928], "\n                          &quot;", [5900, 5928], "\n                          &ldquo;", [5900, 5928], "\n                          &#34;", [5900, 5928], "\n                          &rdquo;", [5950, 5976], "&quot;\n                        ", [5950, 5976], "&ldquo;\n                        ", [5950, 5976], "&#34;\n                        ", [5950, 5976], "&rdquo;\n                        ", "useMotionValue", [82, 98], "useSpring", [98, 109], "intensity", [360, 379], [716, 816], "animated", [3417, 3436], [851, 874], [2196, 2203], [6027, 6072], "\n                      &quot;\n                    ", [6027, 6072], "\n                      &ldquo;\n                    ", [6027, 6072], "\n                      &#34;\n                    ", [6027, 6072], "\n                      &rdquo;\n                    ", [6248, 6293], [6248, 6293], [6248, 6293], [6248, 6293], "testimonialId", [8767, 8784], "position", [509, 538], "useCallback", [79, 92], [56, 65], [159, 221], "NavLink", [277, 337], "mainNavItems", [338, 392], "isVisible", [854, 863], "handleDropdownToggle", [1999, 2154], "handleMouseEnter", [2158, 2313], "handleMouseLeave", [2317, 2445], [3546, 3553], [107, 169], "src", [274, 278], "alt", [277, 282], "props", [298, 308], [9483, 9488], "[]", [1109, 1159], [5686, 5696], "[pathname, trackMetric]", "event", [5973, 5978], [9430, 9487], [2765, 2873], [3560, 3611], [3663, 3713], "useState", [48, 58], "useEffect", [58, 69], [1392, 1445], [2108, 2156], [3914, 3956], [4263, 4305], [4420, 4469], [5636, 5700], "setUpdateAvailable", [10655, 10675], "CACHE_NAME", [111, 155], "CACHE_STRATEGIES", [860, 1065], [1374, 1418], [1666, 1719], [1857, 1901], [2234, 2280], [2403, 2456], [4004, 4048], [4925, 4969], [5954, 6005], [6697, 6748], [7316, 7372], [7987, 8040], [8652, 8700], [8739, 8789], [9247, 9300], [9339, 9387], [9933, 9983], [10800, 10844], [11091, 11142], [11690, 11750], [11770, 11820], [11845, 11898], [1875, 1885], "\n        &quot;", [1875, 1885], "\n        &ldquo;", [1875, 1885], "\n        &#34;", [1875, 1885], "\n        &rdquo;", [1906, 1914], "&quot;\n      ", [1906, 1914], "&ldquo;\n      ", [1906, 1914], "&#34;\n      ", [1906, 1914], "&rdquo;\n      ", [3700, 3719], [108, 133], "competitorData", [218, 232], "setCompetitorData", [232, 251], "rankingData", [278, 289], "keywordGaps", [332, 343], "contentGaps", [386, 397], "COMPETITORS", [561, 2649], "BACKLINK_OPPORTUNITIES", [8561, 10462], "TECHNICAL_GAPS", [10586, 11554], [13402, 13404], "[CONTENT_OPPORTUNITIES.missingPages, KEYWORD_OPPORTUNITIES.highVolumeLowCompetition]", "DOMINATION_STRATEGIES", [13533, 14625], [2463, 2515], "[title, description, image, url, type, siteKeywords, siteName, siteDescription, siteUrl, siteImage]", "className", [292, 310], [3590, 3597], [2363, 2404], "\n                    &quot;\n                  ", [2363, 2404], "\n                    &ldquo;\n                  ", [2363, 2404], "\n                    &#34;\n                  ", [2363, 2404], "\n                    &rdquo;\n                  ", [2545, 2586], [2545, 2586], [2545, 2586], [2545, 2586], [4354, 4407], "\n                          &quot;\n                        ", [4354, 4407], "\n                          &ldquo;\n                        ", [4354, 4407], "\n                          &#34;\n                        ", [4354, 4407], "\n                          &rdquo;\n                        ", [4560, 4613], [4560, 4613], [4560, 4613], [4560, 4613], [284, 346], "rates", [909, 914], [3630, 3656], "[fetchExchangeRates, fromCurrency, toCurrency]", [3712, 3739], "[inputAmount, exchangeRate, calculateConversion]", [4265, 4321], "climate", [596, 609], "bestTime", [609, 623], "culture", [623, 636], [39, 49], [64, 77], [77, 89], [3754, 3768], "\n            &quot;", [3754, 3768], "\n            &ldquo;", [3754, 3768], "\n            &#34;", [3754, 3768], "\n            &rdquo;", [3775, 3787], "&quot;\n          ", [3775, 3787], "&ldquo;\n          ", [3775, 3787], "&#34;\n          ", [3775, 3787], "&rdquo;\n          ", [1145, 1162], "\n        &quot;\n      ", [1145, 1162], "\n        &ldquo;\n      ", [1145, 1162], "\n        &#34;\n      ", [1145, 1162], "\n        &rdquo;\n      ", [1315, 1327], "\n          &quot;", [1315, 1327], "\n          &ldquo;", [1315, 1327], "\n          &#34;", [1315, 1327], "\n          &rdquo;", [1334, 1344], "&quot;\n        ", [1334, 1344], "&ldquo;\n        ", [1334, 1344], "&#34;\n        ", [1334, 1344], "&rdquo;\n        ", "type", [750, 770], [2390, 2395], [2620, 2719], [2669, 2724], "quality", [700, 716], "sizes", [755, 774], "direction", [608, 636], "onClick", [740, 761], [761, 783], "scale", [864, 892], "breakpoints", [2450, 2499], "scrollRef", [658, 689], [2317, 2411], [4843, 4860], [4843, 4860], [4843, 4860], [4843, 4860], [4957, 4958], [4957, 4958], [4957, 4958], [4957, 4958], "timeOfDay", [331, 340], [1558, 1610], [5464, 5534], "\n            „Każda podróż zaczyna się od pierwszego kroku&quot;\n          ", [5464, 5534], "\n            „Każda podróż zaczyna się od pierwszego kroku&ldquo;\n          ", [5464, 5534], "\n            „Każda podróż zaczyna się od pierwszego kroku&#34;\n          ", [5464, 5534], "\n            „Każda podróż zaczyna się od pierwszego kroku&rdquo;\n          ", "Calendar", [11, 20], "User", [19, 27], "Tag", [27, 34], "Palette", [34, 45], "Sunrise", [45, 56], "Sunset", [56, 66], "Mountain", [66, 78], "Droplet", [78, 89], "Heart", [89, 98], "MapPin", [98, 108], "<PERSON><PERSON><PERSON>", [108, 120], [11, 20], [19, 27], [27, 34], [34, 45], [56, 66], [66, 78], [78, 89], [98, 108], [108, 120], "setOffset", [4091, 4102], [4820, 4867], [4524, 4727], [5580, 5629], [8149, 8198], [8700, 8729], [9379, 9498], [9510, 9581], "keyword", [8180, 8187], [8598, 8604], [1214, 1261], [1300, 1346], [1777, 1833], [2117, 2186], [2338, 2405], [2824, 2876], [3076, 3147], [3717, 3773], [3802, 3864], [4359, 4420], [4449, 4514], [5084, 5143], [5588, 5626], [5655, 5712], [5865, 5909], [8036, 8082], [8130, 8182], [9394, 9439], [9607, 9654]]